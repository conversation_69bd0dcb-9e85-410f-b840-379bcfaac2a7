// قسم التقييمات والتعليقات - تم فصله من الصفحة الرئيسية - زكي الخولي
"use client";
import React, { useState } from "react";
import { useForm } from "react-hook-form";

// مكون عرض التعليقات الشجرية - زكي الخولي
function CommentTree({ comments, reviewId, onToggleCommentLike, onAddReply }) {
  const [replyingTo, setReplyingTo] = useState(null);
  const [replyText, setReplyText] = useState("");
  
  return (
    <div>
      {comments?.map((comment) => (
        <div key={comment.id} className="mt-2 p-2 bg-gray-100 rounded ml-2">
          <div className="flex justify-between items-center">
            <span className="text-gray-800">{comment.user?.username || "مستخدم"}: {comment.text}</span>
            <div className="flex items-center gap-2">
              <button
                className={`text-sm ${comment.is_liked ? 'text-blue-600' : 'text-gray-600'}`}
                onClick={() => onToggleCommentLike(comment.id)}
              >
                👍 {comment.likes_count || 0}
              </button>
              <button
                className="text-sm text-green-600"
                onClick={() => setReplyingTo(comment.id)}
              >رد</button>
            </div>
          </div>
          {replyingTo === comment.id && (
            <form
              className="flex gap-2 mt-2"
              onSubmit={e => {
                e.preventDefault();
                if (replyText.trim()) {
                  onAddReply(comment.id, replyText, reviewId);
                  setReplyText("");
                  setReplyingTo(null);
                }
              }}
            >
              <input
                className="border rounded p-1 flex-1"
                value={replyText}
                onChange={e => setReplyText(e.target.value)}
                placeholder="اكتب ردك..."
              />
              <button type="submit" className="bg-primary text-white px-2 rounded">إرسال</button>
              <button type="button" className="text-gray-500" onClick={() => setReplyingTo(null)}>إلغاء</button>
            </form>
          )}
          {/* عرض الردود بشكل شجري */}
          {comment.replies && comment.replies.length > 0 && (
            <CommentTree 
              comments={comment.replies} 
              reviewId={reviewId}
              onToggleCommentLike={onToggleCommentLike}
              onAddReply={onAddReply}
            />
          )}
        </div>
      ))}
    </div>
  );
}

export default function ReviewsSection({ 
  reviews, 
  reviewLoading, 
  reviewError, 
  reviewSuccess,
  onSubmitReview,
  onToggleCommentLike,
  onAddReply,
  fetchReviewCommentsHandler,
  updateCommentLikeRecursive
}) {
  const { register, handleSubmit, reset } = useForm();
  const [expandedReviews, setExpandedReviews] = useState({});

  const toggleReviewExpansion = async (reviewId) => {
    setExpandedReviews(prev => ({
      ...prev,
      [reviewId]: !prev[reviewId]
    }));
    
    // تحميل التعليقات عند التوسيع لأول مرة
    if (!expandedReviews[reviewId]) {
      await fetchReviewCommentsHandler(reviewId);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">التقييمات والآراء</h2>
      
      {/* فورم إضافة تقييم جديد */}
      <div className="mb-8 bg-gray-50 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-700 mb-4">أضف تقييمك</h3>
        <form onSubmit={handleSubmit(onSubmitReview)} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              التقييم (من 1 إلى 5)
            </label>
            <select
              {...register("rating", { required: true })}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">اختر التقييم</option>
              <option value="1">⭐ (1)</option>
              <option value="2">⭐⭐ (2)</option>
              <option value="3">⭐⭐⭐ (3)</option>
              <option value="4">⭐⭐⭐⭐ (4)</option>
              <option value="5">⭐⭐⭐⭐⭐ (5)</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              التعليق
            </label>
            <textarea
              {...register("comment", { required: true })}
              rows={4}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="شاركنا رأيك في الكورس..."
            />
          </div>
          <button
            type="submit"
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            إرسال التقييم
          </button>
        </form>
        
        {reviewError && (
          <div className="mt-4 bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-red-600">{reviewError}</p>
          </div>
        )}
        
        {reviewSuccess && (
          <div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-3">
            <p className="text-green-600">{reviewSuccess}</p>
          </div>
        )}
      </div>

      {/* عرض التقييمات الموجودة */}
      {reviewLoading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">جاري تحميل التقييمات...</p>
        </div>
      ) : reviews && reviews.length > 0 ? (
        <div className="space-y-6">
          {reviews.map((review) => (
            <div key={review.id} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-semibold">
                      {review.user?.username?.charAt(0)?.toUpperCase() || "U"}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">
                      {review.user?.username || "مستخدم"}
                    </h4>
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <svg
                          key={i}
                          className={`w-4 h-4 ${
                            i < review.rating ? "text-yellow-400" : "text-gray-300"
                          }`}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                      <span className="ml-2 text-sm text-gray-500">
                        {new Date(review.created_at).toLocaleDateString('ar-EG')}
                      </span>
                    </div>
                  </div>
                </div>
                
                {review.status === 'approved' && (
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                    معتمد
                  </span>
                )}
              </div>
              
              <p className="text-gray-700 mb-3">{review.comment}</p>
              
              {/* زر عرض/إخفاء التعليقات */}
              <button
                onClick={() => toggleReviewExpansion(review.id)}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                {expandedReviews[review.id] ? "إخفاء التعليقات" : "عرض التعليقات"}
              </button>
              
              {/* التعليقات الشجرية */}
              {expandedReviews[review.id] && (
                <div className="mt-4">
                  <CommentTree 
                    comments={review.comments || []} 
                    reviewId={review.id}
                    onToggleCommentLike={onToggleCommentLike}
                    onAddReply={onAddReply}
                  />
                </div>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          لا توجد تقييمات لهذا الكورس بعد. كن أول من يقيم!
        </div>
      )}
    </div>
  );
}
