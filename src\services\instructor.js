// جميع استدعاءات API الخاصة بالمعلم توضع هنا فقط
// مثال لدوال الخدمة، أضف المزيد حسب الحاجة
import axios from "axios";
import { API_BASE_URL } from '../config/api';

export async function fetchInstructorProfile(instructorId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
    Accept: "application/json",
  };
  const response = await axios.get(`${API_BASE_URL}/api/instructors/${instructorId}/`, { headers });
  return response.data;
}

export async function fetchInstructorCourse(courseId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };
  const response = await axios.get(`${API_BASE_URL}/api/courses/${courseId}/`, { headers });
  return response.data;
}

export async function fetchInstructorLessons(courseId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };
  const response = await axios.get(`${API_BASE_URL}/api/courses/${courseId}/lessons/`, { headers });
  if (Array.isArray(response.data)) return response.data;
  if (response.data.results) return response.data.results;
  return [];
}
export async function deleteInstructorLessons(lessonId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };
  await axios.delete(`${API_BASE_URL}/api/lessons/${lessonId}/`, { headers });
}

export async function fetchCourseVideo(lessonId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };
  const response = await axios.get(`${API_BASE_URL}/lesson/${lessonId}/video/`, { headers });
  return response.data;
}

export async function approveReview(reviewId, approve, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };
  await axios.patch(`${API_BASE_URL}/api/reviews/${reviewId}/`, { is_approved: approve }, { headers });
}

export async function deleteReview(reviewId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };
  await axios.delete(`${API_BASE_URL}/api/reviews/${reviewId}/`, { headers });
}

export async function replyToReview(reviewId, reply, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };
  await axios.post(`${API_BASE_URL}/api/reviews/${reviewId}/reply/`, { reply }, { headers });
}

export async function fetchReviewComments(reviewId, token) {
  const headers = { Authorization: `Bearer ${token}` };
  const response = await axios.get(`${API_BASE_URL}/api/reviews/${reviewId}/comments/`, { headers });
  return response.data;
}

export async function addReviewComment(reviewId, text, parent, token) {
  const headers = { Authorization: `Bearer ${token}` };
  await axios.post(`${API_BASE_URL}/api/reviews/${reviewId}/add_comment/`, { text, parent }, { headers });
}

export async function createLessonWithVideo({ formData, videoFile, token, setUploadProgress }) {
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://127.0.0.1:8000";
  // Prepare lesson data
  const lessonFormData = new FormData();
  lessonFormData.append("title", formData.title.trim());
  lessonFormData.append("content", formData.content.trim());
  lessonFormData.append("order", parseInt(formData.order) || 1);
  lessonFormData.append("lesson_type", formData.lessonType);
  lessonFormData.append("is_preview", formData.isPreview || false);
  lessonFormData.append("duration", parseInt(formData.duration) || 0);
  lessonFormData.append("is_drm_protected", "true");
  lessonFormData.append("is_hls_encrypted", "true");
  lessonFormData.append("token_expiry_hours", "24");
  lessonFormData.append("watermark_enabled", "true");
  lessonFormData.append("course", formData.course);

  // Create lesson
  const lessonResponse = await axios.post(
    `${API_BASE_URL}/api/lessons/`,
    lessonFormData,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "multipart/form-data",
      },
    }
  );
  if (!lessonResponse.data) {
    throw new Error("لم يتم استلام تأكيد إنشاء الدرس من الخادم");
  }
  const lessonId = lessonResponse.data.id;

  // Upload video if present and lesson type is video
  if (videoFile && formData.lessonType === "video") {
    const videoFormData = new FormData();
    videoFormData.append("video", videoFile);
    await axios.post(
      `${API_BASE_URL}/api/lessons/${lessonId}/upload_video/`,
      videoFormData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "multipart/form-data",
        },
        onUploadProgress: (progressEvent) => {
          if (setUploadProgress && progressEvent.total) {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            setUploadProgress(percentCompleted);
          }
        },
      }
    );
    // Optionally, fetch video URL if needed
    // const videoUrlResponse = await axios.get(
    //   `${API_BASE_URL}/api/lessons/${lessonId}/video_url/`,
    //   { headers: { Authorization: `Bearer ${token}` } }
    // );
    // return { lesson: lessonResponse.data, videoUrl: videoUrlResponse.data };
  }
  return lessonResponse.data;
}

/**
 * جلب جميع الدروس لدورة معينة
 */
export async function fetchLessonsByCourse(courseId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };
  const response = await axios.get(`${API_BASE_URL}/api/courses/${courseId}/lessons/`, { headers });
  if (Array.isArray(response.data)) return response.data;
  if (response.data.results) return response.data.results;
  return [];
}

/**
 * جلب رابط الفيديو المؤمن لدرس معين
 */
export async function fetchLessonVideoUrl(lessonId, token) {
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  };
  const response = await axios.get(`${API_BASE_URL}/api/lessons/${lessonId}/video_url/`, { headers });
  return response.data;
}
// Hossam test hls error
