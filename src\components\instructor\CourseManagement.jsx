// مكون إدارة الكورس للمعلم - تم فصله من الصفحة الرئيسية - زكي الخولي
"use client";
import React, { useState } from "react";
import Image from "next/image";
import Cookies from "js-cookie";
import { API_BASE_URL } from '../../config/api';

export default function CourseManagement({ 
  courseData, 
  onPublish,
  publishing 
}) {
  const [showPublishModal, setShowPublishModal] = useState(false);

  // دالة نشر الكورس مع إصلاح المشكلة - زكي الخولي
  const handlePublish = async () => {
    try {
      const token = Cookies.get("authToken");
      if (!token) {
        alert("يرجى تسجيل الدخول أولاً");
        return;
      }

      // استخدام API الصحيح بدلاً من process.env - زكي الخولي
      const response = await fetch(`${API_BASE_URL}/api/courses/${courseData.id}/publish/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        alert(result.message || "تم نشر الكورس بنجاح!");
        if (onPublish) {
          onPublish();
        }
      } else {
        const errorData = await response.json();
        alert(errorData.error || "حدث خطأ أثناء نشر الكورس");
      }
    } catch (error) {
      console.error("خطأ في نشر الكورس - زكي الخولي:", error);
      alert("حدث خطأ أثناء نشر الكورس");
    }
    setShowPublishModal(false);
  };

  if (!courseData) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="h-4 bg-gray-200 rounded mb-2"></div>
          <div className="h-4 bg-gray-200 rounded mb-2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      {/* صورة الكورس */}
      <div className="relative h-64 w-full bg-gray-200">
        {courseData.thumbnail ? (
          <Image
            src={`https://res.cloudinary.com/di5y7hnub/${courseData.thumbnail}`}
            alt={courseData.title}
            fill
            className="object-cover"
            priority
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <span className="text-gray-500">لا توجد صورة للكورس</span>
          </div>
        )}
      </div>

      {/* معلومات الكورس */}
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              {courseData.title}
            </h1>
            <p className="text-gray-600 mb-2">
              {courseData.short_description}
            </p>
            <div className="flex items-center space-x-4 rtl:space-x-reverse text-sm text-gray-500">
              <span>المستوى: {courseData.level}</span>
              <span>اللغة: {courseData.language}</span>
              <span>السعر: {courseData.price} {courseData.currency}</span>
            </div>
          </div>
          
          {/* حالة النشر */}
          <div className="flex flex-col items-end gap-2">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              courseData.is_published 
                ? 'bg-green-100 text-green-800' 
                : 'bg-yellow-100 text-yellow-800'
            }`}>
              {courseData.is_published ? 'منشور' : 'غير منشور'}
            </span>
            
            {!courseData.is_published && (
              <button
                onClick={() => setShowPublishModal(true)}
                disabled={publishing}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {publishing ? 'جاري النشر...' : 'نشر الكورس'}
              </button>
            )}
          </div>
        </div>

        {/* وصف الكورس */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">وصف الكورس</h3>
          <p className="text-gray-600 leading-relaxed">
            {courseData.description}
          </p>
        </div>

        {/* إحصائيات الكورس */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-1">الطلاب</h4>
            <p className="text-2xl font-bold text-blue-600">
              {courseData.students_count || 0}
            </p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-semibold text-green-800 mb-1">التقييم</h4>
            <p className="text-2xl font-bold text-green-600">
              {courseData.rating || 'لا يوجد'}
            </p>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <h4 className="font-semibold text-purple-800 mb-1">الإعجابات</h4>
            <p className="text-2xl font-bold text-purple-600">
              {courseData.likes_count || 0}
            </p>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg">
            <h4 className="font-semibold text-orange-800 mb-1">المراجعات</h4>
            <p className="text-2xl font-bold text-orange-600">
              {courseData.reviews?.length || 0}
            </p>
          </div>
        </div>

        {/* أزرار الإدارة */}
        <div className="flex flex-wrap gap-3">
          <button className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
            تعديل الكورس
          </button>
          <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
            إضافة درس جديد
          </button>
          <button className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
            إدارة الامتحانات
          </button>
          <button className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors">
            عرض التقارير
          </button>
        </div>
      </div>

      {/* مودال تأكيد النشر */}
      {showPublishModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-bold text-gray-900 mb-4">
              تأكيد نشر الكورس
            </h3>
            <p className="text-gray-600 mb-6">
              هل أنت متأكد من نشر هذا الكورس؟ سيصبح متاحاً لجميع الطلاب بعد النشر.
            </p>
            <div className="flex gap-3 justify-end">
              <button
                onClick={() => setShowPublishModal(false)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                إلغاء
              </button>
              <button
                onClick={handlePublish}
                disabled={publishing}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {publishing ? 'جاري النشر...' : 'نشر الكورس'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
