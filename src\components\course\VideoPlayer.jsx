// مشغل الفيديو المحمي مع HLS - تم فصله من الصفحة الرئيسية - زكي الخولي
"use client";
import React, { useEffect, useRef, useState } from "react";
import Hls from "hls.js";

export default function VideoPlayer({ 
  videoUrl, 
  isClient, 
  onError,
  selectedLesson 
}) {
  const videoRef = useRef(null);
  const [player, setPlayer] = useState(null);

  // حماية من keyboard shortcuts - زكي الخولي
  useEffect(() => {
    const handleKeyDown = (e) => {
      // منع Ctrl+S, Ctrl+Shift+I, F12, Ctrl+U
      if (
        (e.ctrlKey && e.key === 's') ||
        (e.ctrlKey && e.shiftKey && e.key === 'I') ||
        e.key === 'F12' ||
        (e.ctrlKey && e.key === 'u')
      ) {
        e.preventDefault();
        console.log('محاولة استخدام keyboard shortcut محظورة - زكي الخولي');
      }
    };

    if (videoUrl) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [videoUrl]);

  // تهيئة مشغل الفيديو المحمي مع HLS - زكي الخولي
  useEffect(() => {
    if (!isClient || !videoUrl || !videoRef.current) return;

    // تنظيف المشغل السابق
    if (player) {
      player.destroy();
      setPlayer(null);
    }

    const video = videoRef.current;
    video.crossOrigin = "anonymous";

    console.log('تهيئة مشغل الفيديو المحمي - زكي الخولي');

    const initSecurePlayer = async () => {
      try {
        // التحقق من دعم HLS
        if (Hls.isSupported()) {
          console.log('استخدام HLS.js للفيديو المحمي - زكي الخولي');
          const hls = new Hls({
            // إعدادات الأمان - زكي الخولي
            enableWorker: true,
            lowLatencyMode: false,
            backBufferLength: 90,
            maxBufferLength: 30,
            maxMaxBufferLength: 600,
          });

          hls.loadSource(videoUrl);
          hls.attachMedia(video);

          hls.on(Hls.Events.MANIFEST_PARSED, async () => {
            console.log('تم تحليل manifest بنجاح - زكي الخولي');
            // تهيئة Plyr بعد تحميل HLS
            const PlyrModule = await import("plyr");
            const newPlayer = new PlyrModule.default(video, {
              controls: [
                "play-large",
                "play",
                "progress",
                "current-time",
                "mute",
                "volume",
                "settings",
                "fullscreen",
              ],
              download: false, // منع التحميل - زكي الخولي
              hideControls: true,
              keyboard: { focused: true, global: true },
              tooltips: { controls: true, seek: true },
              quality: {
                default: 720,
                options: [1080, 720, 480, 360],
              },
              // إعدادات إضافية للحماية - زكي الخولي
              clickToPlay: true,
              disableContextMenu: true,
            });

            setPlayer(newPlayer);
          });

          hls.on(Hls.Events.ERROR, (_, data) => {
            console.error('خطأ في HLS - زكي الخولي:', data);
            if (data.fatal) {
              switch (data.type) {
                case Hls.ErrorTypes.NETWORK_ERROR:
                  onError("خطأ في الشبكة أثناء تشغيل الفيديو");
                  break;
                case Hls.ErrorTypes.MEDIA_ERROR:
                  onError("خطأ في ملف الفيديو");
                  break;
                default:
                  onError("حدث خطأ أثناء تشغيل الفيديو");
                  break;
              }
            }
          });

        } else if (video.canPlayType("application/vnd.apple.mpegurl")) {
          // Safari native HLS support
          console.log('استخدام HLS الأصلي في Safari - زكي الخولي');
          video.src = videoUrl;

          video.addEventListener("loadedmetadata", async () => {
            const PlyrModule = await import("plyr");
            const newPlayer = new PlyrModule.default(video, {
              controls: [
                "play-large",
                "play",
                "progress",
                "current-time",
                "mute",
                "volume",
                "settings",
                "fullscreen",
              ],
              download: false,
              hideControls: true,
              keyboard: { focused: true, global: true },
              tooltips: { controls: true, seek: true },
              disableContextMenu: true,
            });

            setPlayer(newPlayer);
          });
        } else {
          onError("المتصفح لا يدعم تشغيل فيديوهات HLS المحمية");
        }

        // معالجة أخطاء الفيديو
        video.addEventListener("error", () => {
          onError("حدث خطأ أثناء تشغيل الفيديو");
        });

      } catch (error) {
        console.error('خطأ في تهيئة المشغل - زكي الخولي:', error);
        onError("فشل في تهيئة مشغل الفيديو");
      }
    };

    initSecurePlayer();

    return () => {
      if (player) {
        player.destroy();
      }
    };
  }, [videoUrl, isClient, onError, player]);

  if (!videoUrl) {
    return (
      <div className="w-full h-64 bg-gray-100 flex items-center justify-center rounded-lg">
        <span className="text-gray-500">اختر درساً لمشاهدة الفيديو</span>
      </div>
    );
  }

  return (
    <div className="video-protected">
      {/* إضافة styles الحماية - زكي الخولي */}
      <style jsx>{`
        .video-protected {
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: transparent;
          pointer-events: auto;
        }

        .video-protected video {
          pointer-events: auto;
        }

        /* منع النقر بالزر الأيمن */
        .video-protected video::-webkit-media-controls {
          display: none !important;
        }

        .video-protected video::-webkit-media-controls-enclosure {
          display: none !important;
        }

        /* منع حفظ الفيديو */
        .video-protected video::-webkit-media-controls-download-button {
          display: none !important;
        }

        .video-protected video::-webkit-media-controls-fullscreen-button {
          display: none !important;
        }
      `}</style>
      
      <div className="relative">
        <video
          ref={videoRef}
          className="w-full h-auto rounded-lg"
          controls={false}
          playsInline
          onContextMenu={(e) => e.preventDefault()}
        />
        {selectedLesson && (
          <div className="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white px-3 py-1 rounded">
            {selectedLesson.title}
          </div>
        )}
      </div>
    </div>
  );
}
