// Custom hook لإدارة مودال الامتحان - زكي الخولي
import { useState } from "react";
import Cookies from "js-cookie";
import {
  fetchExamTimeLeft,
  fetchExamStatus,
  startExam,
  checkTimeAndAutoSubmit,
} from "../services/student";

export function useExamModal() {
  const [modalOpen, setModalOpen] = useState(false);
  const [modalType, setModalType] = useState(null); // 'exam' | 'assignment'
  const [modalId, setModalId] = useState(null);
  const [modalDuration, setModalDuration] = useState(0);
  const [modalQuestions, setModalQuestions] = useState([]);
  const [modalExamStatus, setModalExamStatus] = useState(null);

  // فتح مودال الامتحان مع تنفيذ المتطلبات - زكي الخولي
  const handleOpenExamModal = async (examIdParam, onExamSubmitted) => {
    console.log('زر حل الامتحان تم الضغط عليه - زكي الخولي');
    try {
      const token = Cookies.get("authToken");
      console.log('examId:', examIdParam);
      console.log('token:', token);
      
      if (token && examIdParam) {
        // تحقق من حالة الامتحان أولاً - زكي الخولي
        const statusRes = await fetchExamStatus(examIdParam, token);
        console.log('زكي الخولي - fetchExamStatus response:', statusRes);

        if (statusRes.status === 'submitted') {
          // الامتحان اتسلم خلاص، اعرض النتيجة مع إجابات الطالب - زكي الخولي
          console.log('الامتحان مُسلم، سيتم عرض النتيجة - زكي الخولي');
          setModalType('exam');
          setModalId(examIdParam);
          setModalDuration(0); // مافيش وقت لأن الامتحان خلص
          setModalQuestions(statusRes.questions || []);
          setModalExamStatus(statusRes);
          setModalOpen(true);
        } else {
          // الامتحان لسه مش اتسلم، ابدأ الامتحان أو استكمل - زكي الخولي
          try {
            // استدعاء start endpoint لتسجيل دخول الطالب في وقت كذا - زكي الخولي
            const startRes = await startExam(examIdParam, token);
            console.log('تم تسجيل دخول الطالب للامتحان - زكي الخولي:', startRes);
          } catch (startErr) {
            if (startErr?.response?.status === 403) {
              // الامتحان تم تسليمه بالفعل
              console.log('زكي الخولي - الامتحان تم تسليمه بالفعل');
              const updatedStatus = await fetchExamStatus(examIdParam, token);
              setModalType('exam');
              setModalId(examIdParam);
              setModalDuration(0);
              setModalQuestions(updatedStatus.questions || []);
              setModalExamStatus(updatedStatus);
              setModalOpen(true);
              return;
            }
          }

          // جلب الوقت المتبقي - زكي الخولي
          const res = await fetchExamTimeLeft(examIdParam, token);
          console.log('زكي الخولي - fetchExamTimeLeft response:', res);
          console.log('زكي الخولي - res.questions:', res.questions);

          if (res.status === 'auto_submitted') {
            // تم التسليم التلقائي في الباك إند، اعرض النتيجة - زكي الخولي
            alert(res.message);
            // إعادة جلب حالة الامتحان لعرض النتيجة
            const updatedStatus = await fetchExamStatus(examIdParam, token);
            setModalType('exam');
            setModalId(examIdParam);
            setModalDuration(0);
            setModalQuestions(updatedStatus.questions || []);
            setModalExamStatus(updatedStatus);
            setModalOpen(true);
          } else if (res.time_left === -1) {
            // واجب بوقت غير محدود - زكي الخولي
            console.log('فتح الواجب بوقت غير محدود - زكي الخولي');
            setModalType('assignment');
            setModalId(examIdParam);
            setModalDuration(-1); // وقت غير محدود
            setModalQuestions(res.questions || []);
            setModalExamStatus(null);
            setModalOpen(true);
          } else if (res.time_left > 0) {
            console.log('فتح الامتحان مع الوقت المتبقي - زكي الخولي:', res.time_left);
            // إظهار المودال للطالب لبدء الامتحان - زكي الخولي
            setModalType('exam');
            setModalId(examIdParam);
            setModalDuration(res.time_left); // الوقت المتبقي الصحيح - زكي الخولي
            setModalQuestions(res.questions || []);
            setModalExamStatus(null);
            setModalOpen(true);
          } else {
            alert("انتهى وقت الامتحان أو لا يمكنك الدخول.");
          }
        }
      }
    } catch (e) {
      console.error('خطأ في فتح الامتحان - زكي الخولي:', e);
      alert(e?.response?.data?.error || "لا يمكنك دخول الامتحان أو انتهى الوقت.");
    }
  };

  // إغلاق المودال - زكي الخولي
  const handleCloseModal = () => {
    setModalOpen(false);
    setModalType(null);
    setModalId(null);
    setModalDuration(0);
    setModalQuestions([]);
    setModalExamStatus(null);
  };

  // معالجة تسليم الامتحان - زكي الخولي
  const handleExamSubmittedInModal = (examId, onExamSubmitted) => {
    if (onExamSubmitted) {
      onExamSubmitted(examId);
    }
    // لا نغلق المودال هنا، سيتم إظهار النتيجة
  };

  return {
    modalOpen,
    modalType,
    modalId,
    modalDuration,
    modalQuestions,
    modalExamStatus,
    handleOpenExamModal,
    handleCloseModal,
    handleExamSubmittedInModal,
  };
}
