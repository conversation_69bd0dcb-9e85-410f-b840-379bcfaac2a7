// مودال الامتحان والواجب - تم فصله من الصفحة الرئيسية - زكي الخولي
"use client";
import React, { useEffect, useState } from "react";
import Cookies from "js-cookie";
import {
  fetchExamTimeLeft,
  fetchExamStatus,
  saveExamAnswer,
  submitExam,
  startExam,
  checkTimeAndAutoSubmit,
} from "../../services/student";

export default function ExamModal({ 
  open, 
  onClose, 
  type, 
  id, 
  duration, 
  questions: propQuestions, 
  examStatus, 
  onExamSubmitted 
}) {
  const [questions, setQuestions] = useState([]);
  const [answers, setAnswers] = useState({});
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [result, setResult] = useState(null);
  const [timer, setTimer] = useState(0);
  const [fetchError, setFetchError] = useState(null);
  const [feedbackMap, setFeedbackMap] = useState({});

  // تهيئة المودال عند الفتح - زكي الخولي
  useEffect(() => {
    console.log('زكي الخولي - ExamModal useEffect:', { open, type, id, duration, timer });
    if (open) {
      setResult(null);
      setFetchError(null);
      setFeedbackMap({});

      // تحقق إذا كان examStatus يشير إلى أن الامتحان اتسلم - زكي الخولي
      if (examStatus && examStatus.status === 'submitted') {
        setResult({
          message: `تم تسليم الامتحان بنجاح! النتيجة: ${examStatus.score}/${examStatus.max_score}`,
          correctCount: examStatus.correctCount || 0,
          score: examStatus.score,
          max_score: examStatus.max_score,
          answers_feedback: examStatus.answers_feedback || [],
          passed: examStatus.passed,
          totalQuestions: examStatus.totalQuestions || examStatus.questions?.length || 0
        });
        setQuestions(examStatus.questions || []);
        setAnswers(examStatus.answers || {});
        setLoading(false);
        return;
      }

      // تحميل الإجابات المحفوظة من الباك إند فقط - زكي الخولي
      if (type === "exam") {
        loadSavedAnswers();
      }

      // استخدم الأسئلة من props إذا توفرت
      if (Array.isArray(propQuestions) && propQuestions.length > 0) {
        setQuestions(propQuestions);
        setLoading(false);
      } else {
        setQuestions([]);
        setLoading(false);
        setFetchError("لا توجد أسئلة متاحة لهذا الامتحان/الواجب");
      }

      // تعيين الـ timer - زكي الخولي
      if (type === "exam" && duration > 0) {
        setTimer(duration);
        console.log('زكي الخولي - تم تعيين الـ timer إلى:', duration);
      } else if (type === "assignment") {
        setTimer(-1); // وقت غير محدود للواجبات
        console.log('زكي الخولي - واجب بوقت غير محدود');
      }
    }
  }, [open, type, id, duration, propQuestions, examStatus]);

  // تحميل الإجابات المحفوظة - زكي الخولي
  const loadSavedAnswers = async () => {
    try {
      const token = Cookies.get("authToken");
      const timeRes = await fetchExamTimeLeft(id, token);
      if (timeRes.saved_answers) {
        const convertedAnswers = {};
        Object.keys(timeRes.saved_answers).forEach(qid => {
          const answerId = timeRes.saved_answers[qid];
          const question = propQuestions?.find(q => q.id == qid);
          if (question && question.answers) {
            const answerIndex = question.answers.findIndex(a => a.id == answerId);
            if (answerIndex !== -1) {
              convertedAnswers[qid] = answerIndex;
            }
          }
        });
        setAnswers(convertedAnswers);
        console.log('زكي الخولي - تم تحميل الإجابات من الباك إند:', convertedAnswers);
      }
    } catch (e) {
      console.log('زكي الخولي - لم يتم العثور على إجابات محفوظة في الباك إند');
    }
  };

  // العد التنازلي للامتحانات - زكي الخولي
  useEffect(() => {
    if (!open || result) return;

    if (type === "exam" && timer > 0) {
      const interval = setInterval(() => setTimer((t) => {
        if (t <= 1) {
          console.log('الوقت انتهى في الفرونت إند، سيتم التسليم التلقائي - زكي الخولي');
          handleAutoSubmit();
          return 0;
        }
        return t - 1;
      }), 1000);
      return () => clearInterval(interval);
    }
  }, [timer, open, type, duration, result]);

  // إعداد feedbackMap عند استلام النتيجة - زكي الخولي
  useEffect(() => {
    if (result && result.answers_feedback) {
      const map = {};
      result.answers_feedback.forEach(fb => {
        map[fb.question_id] = {
          is_correct: fb.is_correct,
          correct_answer_id: fb.correct_answer_id,
          selected_answer_id: fb.selected_answer_id,
          correct_answer_text: fb.correct_answer_text,
          selected_answer_text: fb.selected_answer_text
        };
      });
      setFeedbackMap(map);
    }
  }, [result]);

  // تحديث الإجابة مع منع الإرسال المتكرر - زكي الخولي
  const handleChange = (qid, choiceIdx) => {
    setAnswers((prev) => {
      if (prev[qid] === choiceIdx) {
        console.log('الإجابة لم تتغير للسؤال، لن يتم الإرسال - زكي الخولي:', qid);
        return prev;
      }

      const updated = { ...prev, [qid]: choiceIdx };

      // إرسال الإجابة للباك اند مرة واحدة فقط عند التغيير - زكي الخولي
      (async () => {
        try {
          const token = Cookies.get("authToken");
          const q = questions.find(q => q.id == qid);
          let answer_id = null;
          if (q && Array.isArray(q.answers) && q.answers[choiceIdx]) {
            answer_id = q.answers[choiceIdx].id;
          }
          console.log('حفظ إجابة جديدة في الباك إند - زكي الخولي:', qid, 'من', prev[qid], 'إلى', choiceIdx);

          await saveExamAnswer(id, qid, answer_id, token);
          console.log('تم حفظ الإجابة بنجاح في الباك إند - زكي الخولي');
        } catch (e) {
          console.error('خطأ في حفظ الإجابة في الباك إند - زكي الخولي:', e);
        }
      })();
      return updated;
    });
  };

  // تسليم الامتحان يدوياً - زكي الخولي
  const handleSubmit = async () => {
    if (submitting || result || fetchError || questions.length === 0) {
      console.log('زكي الخولي - منع التسليم:', { submitting, result, fetchError, questionsLength: questions.length });
      return;
    }

    if (type === "exam") {
      const answeredQuestions = Object.keys(answers).length;
      if (answeredQuestions === 0) {
        alert("يرجى الإجابة على سؤال واحد على الأقل قبل التسليم");
        return;
      }

      if (!confirm(`هل أنت متأكد من تسليم الامتحان؟ تم الإجابة على ${answeredQuestions} من ${questions.length} سؤال.`)) {
        return;
      }
    }

    setSubmitting(true);
    console.log('بدء تسليم الامتحان يدوياً - زكي الخولي:', id);
    const token = Cookies.get("authToken");
    try {
      const res = await submitExam(id, token);
      setResult(res);
      console.log('تم تسليم الامتحان بنجاح - زكي الخولي:', res);
      if (onExamSubmitted) {
        onExamSubmitted(id);
      }
    } catch (err) {
      console.error('خطأ في تسليم الامتحان - زكي الخولي:', err);
      if (err?.response && err.response.data) {
        alert(err.response.data.detail || err.response.data.error || JSON.stringify(err.response.data));
        console.error('تفاصيل خطأ التسليم:', err.response.data);
      } else {
        alert("حدث خطأ أثناء التسليم");
        console.error('خطأ غير معروف أثناء التسليم:', err);
      }
    }
    setSubmitting(false);
  };

  // التسليم التلقائي عند انتهاء الوقت - زكي الخولي
  const handleAutoSubmit = async () => {
    if (submitting || result) return;

    if (type === "assignment") {
      console.log('الواجب ليس له تسليم تلقائي - زكي الخولي');
      return;
    }

    setSubmitting(true);
    const token = Cookies.get("authToken");

    console.log('بدء التسليم التلقائي للامتحان في الفرونت إند - زكي الخولي:', id);

    try {
      const autoSubmitRes = await checkTimeAndAutoSubmit(id, token);
      console.log('checkTimeAndAutoSubmit response - زكي الخولي:', autoSubmitRes);

      if (autoSubmitRes.status === 'auto_submitted') {
        console.log('تم التسليم التلقائي بنجاح، جلب النتيجة - زكي الخولي');
        const statusRes = await fetchExamStatus(id, token);
        setResult({
          message: "تم تسليم الامتحان تلقائياً لانتهاء الوقت المحدد.",
          auto_submitted: true,
          score: statusRes.score,
          max_score: statusRes.max_score,
          answers_feedback: statusRes.answers_feedback || [],
          correctCount: statusRes.answers_feedback ? statusRes.answers_feedback.filter(f => f.is_correct).length : 0,
          passed: statusRes.passed,
          totalQuestions: statusRes.totalQuestions || questions.length
        });

        if (onExamSubmitted) {
          onExamSubmitted(id);
        }
      } else {
        console.log('الوقت لم ينته بعد في الباك إند - زكي الخولي');
      }
    } catch (err) {
      console.error('خطأ في التسليم التلقائي - زكي الخولي:', err);

      try {
        const res = await submitExam(id, token);
        setResult({
          ...res,
          message: "تم تسليم الامتحان تلقائياً لانتهاء الوقت المحدد.",
          auto_submitted: true
        });

        if (onExamSubmitted) {
          onExamSubmitted(id);
        }
      } catch (submitErr) {
        console.error('خطأ في التسليم العادي - زكي الخولي:', submitErr);
        setResult({
          message: "انتهى وقت الامتحان. تم حفظ إجاباتك المتاحة.",
          auto_submitted: true,
          score: 0,
          max_score: questions.length * 10,
          answers_feedback: []
        });
      }
    }
    setSubmitting(false);
  };

  // إغلاق المودال وتنظيف البيانات - زكي الخولي
  const handleClose = () => {
    setResult(null);
    setAnswers({});
    setFetchError(null);
    setQuestions([]);
    setFeedbackMap({});
    onClose();
  };

  // تنسيق الوقت - زكي الخولي
  const formatTime = (seconds) => {
    const m = Math.floor(seconds / 60).toString().padStart(2, "0");
    const s = (seconds % 60).toString().padStart(2, "0");
    return `${m}:${s}`;
  };

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-lg max-w-2xl w-full p-6 relative animate-fadeIn">
        <button
          onClick={handleClose}
          className="absolute left-4 top-4 text-gray-400 hover:text-gray-700 text-2xl"
          aria-label="إغلاق"
        >
          ×
        </button>
        <h2 className="text-xl font-bold mb-4 text-center">
          {type === "exam" ? "امتحان" : type === "assignment" ? "واجب" : "اختبار"} الكورس
        </h2>
        
        {loading ? (
          <div className="text-center py-8">جاري التحميل...</div>
        ) : fetchError ? (
          <div className="text-center text-red-600 py-8">{fetchError}</div>
        ) : result ? (
          <div className="text-center space-y-4 py-6">
            {result.auto_submitted && (
              <div className="bg-yellow-100 border border-yellow-400 text-yellow-800 px-4 py-3 rounded mb-4">
                ⏰ تم تسليم الامتحان تلقائياً لانتهاء الوقت المحدد
              </div>
            )}
            <div className="space-y-3">
              <div className="text-lg">
                <b>عدد الإجابات الصحيحة:</b>
                <span className="text-green-600 font-bold"> {result.correctCount}</span>
                <span className="text-gray-600"> من {result.totalQuestions || questions.length}</span>
              </div>
              <div className="text-lg">
                <b>الدرجة النهائية:</b>
                <span className="font-bold"> {result.score} / {result.max_score}</span>
              </div>
              <div className="text-lg">
                <b>النتيجة:</b>
                <span className={result.passed ? "text-green-600 font-bold" : "text-red-600 font-bold"}>
                  {result.passed ? " نجح" : " لم ينجح"}
                </span>
              </div>
            </div>
            
            {/* عرض تصحيح الأسئلة مع إجابات الطالب مُعلمة - زكي الخولي */}
            <div className="mt-6 text-right">
              <h3 className="text-lg font-bold mb-4 text-gray-800">تفاصيل الإجابات:</h3>
              {questions.map((q, idx) => {
                const feedback = feedbackMap[q.id];
                const isCorrect = feedback?.is_correct || false;
                const choices = Array.isArray(q.choices)
                  ? q.choices
                  : Array.isArray(q.answers)
                  ? q.answers.map(a => a.text)
                  : [];
                const selectedIdx = answers[q.id];

                let correctIdx = null;
                if (feedback?.correct_answer_id && Array.isArray(q.answers)) {
                  correctIdx = q.answers.findIndex(a => a.id === feedback.correct_answer_id);
                } else if (Array.isArray(q.answers)) {
                  correctIdx = q.answers.findIndex(a => a.is_correct);
                } else if (Array.isArray(q.choices) && q.choices.findIndex) {
                  correctIdx = q.choices.findIndex(c => c.is_correct);
                }
                
                return (
                  <div
                    key={q.id}
                    className={`border rounded p-4 mb-2 ${
                      isCorrect === true
                        ? 'bg-green-50 border-green-400'
                        : isCorrect === false
                        ? 'bg-red-50 border-red-400'
                        : ''
                    }`}
                  >
                    <div className="mb-2 font-semibold">
                      {idx + 1}. {q.title || q.text}
                    </div>
                    {/* عرض صورة السؤال في النتائج إذا كانت موجودة - زكي الخولي */}
                    {q.image_url && (
                      <div className="mb-3">
                        <img
                          src={q.image_url}
                          alt="صورة السؤال"
                          className="max-w-full h-48 object-contain border rounded"
                        />
                      </div>
                    )}
                    <div className="space-y-2">
                      {choices.map((choice, i) => {
                        const isSelected = selectedIdx === i;
                        const isRight = correctIdx === i;
                        let choiceClass = '';
                        
                        if (isSelected && isCorrect === true) choiceClass = 'text-green-700 font-bold';
                        else if (isSelected && isCorrect === false) choiceClass = 'text-red-700 font-bold';
                        if (isCorrect === false && isRight) choiceClass = 'bg-green-100 text-green-800 font-bold rounded px-2 py-1';
                        
                        return (
                          <div key={i} className={choiceClass + ' flex items-center gap-2'}>
                            <input
                              type="radio"
                              name={`q_${q.id}`}
                              value={i}
                              checked={isSelected}
                              readOnly
                              disabled
                            />{' '}
                            <span>{choice}</span>
                            {isCorrect === false && isRight && (
                              <span className="ml-2 text-green-600 text-lg font-bold">✔</span>
                            )}
                          </div>
                        );
                      })}
                    </div>
                    {isCorrect === true && (
                      <div className="mt-2 text-green-700 font-bold">إجابة صحيحة</div>
                    )}
                    {isCorrect === false && (
                      <div className="mt-2 text-red-700 font-bold">إجابة خاطئة</div>
                    )}
                  </div>
                );
              })}
            </div>
            <button onClick={handleClose} className="mt-4 bg-blue-600 text-white px-6 py-2 rounded">إغلاق</button>
          </div>
        ) : (
          <>
            {type === "exam" && timer > 0 && (
              <div className="flex justify-end mb-2">
                <span className="bg-gray-100 px-3 py-1 rounded text-sm font-bold text-red-600">
                  الوقت المتبقي: {formatTime(timer)}
                </span>
              </div>
            )}
            {type === "assignment" && (
              <div className="flex justify-end mb-2">
                <span className="bg-green-100 px-3 py-1 rounded text-sm font-bold text-green-600">
                  واجب - وقت غير محدود
                </span>
              </div>
            )}
            <form
              onSubmit={(e) => {
                e.preventDefault();
                handleSubmit();
              }}
              className="space-y-6"
            >
              {questions.map((q, idx) => {
                const choices = Array.isArray(q.choices)
                  ? q.choices
                  : Array.isArray(q.answers)
                  ? q.answers.map(a => a.text)
                  : [];
                return (
                  <div key={q.id} className="border rounded p-4 mb-2">
                    <div className="mb-2 font-semibold">
                      {idx + 1}. {q.title || q.text}
                    </div>
                    {/* عرض صورة السؤال إذا كانت موجودة - زكي الخولي */}
                    {q.image_url && (
                      <div className="mb-3">
                        <img
                          src={q.image_url}
                          alt="صورة السؤال"
                          className="max-w-full h-48 object-contain border rounded"
                        />
                      </div>
                    )}
                    <div className="space-y-2">
                      {choices.map((choice, i) => (
                        <label key={i} className="flex items-center gap-2 cursor-pointer">
                          <input
                            type="radio"
                            name={`q_${q.id}`}
                            value={i}
                            checked={answers[q.id] === i}
                            onChange={() => handleChange(q.id, i)}
                            disabled={!!result || (type === "exam" && timer === 0)}
                          />
                          <span>{choice}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                );
              })}
              <div className="flex gap-2 justify-center mt-4">
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-6 py-2 rounded disabled:opacity-50"
                  disabled={submitting || (type === "exam" && timer === 0 && duration > 0) || result}
                >
                  {submitting ? "جاري التسليم..." : type === "assignment" ? "تسليم الواجب" : "تسليم الامتحان"}
                </button>
                <button
                  type="button"
                  className="bg-gray-200 text-gray-700 px-6 py-2 rounded"
                  onClick={handleClose}
                  disabled={submitting}
                >
                  إغلاق
                </button>
              </div>
            </form>
          </>
        )}
      </div>
    </div>
  );
}
