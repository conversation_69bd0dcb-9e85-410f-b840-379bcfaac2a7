// مكون إدارة التقييمات للمعلم - تم فصله من الصفحة الرئيسية - زكي الخولي
"use client";
import React, { useState } from "react";

export default function ReviewsManagement({ 
  reviews, 
  onApproveReview,
  onDeleteReview,
  onReplyToReview,
  approving,
  reviewComments,
  onLoadComments,
  onAddComment
}) {
  const [expandedReview, setExpandedReview] = useState(null);
  const [replyText, setReplyText] = useState({});
  const [commentText, setCommentText] = useState({});
  const [replying, setReplying] = useState({});
  const [commenting, setCommenting] = useState({});

  const handleReply = async (reviewId) => {
    if (!replyText[reviewId]?.trim()) return;
    
    setReplying(prev => ({ ...prev, [reviewId]: true }));
    try {
      await onReplyToReview(reviewId, replyText[reviewId]);
      setReplyText(prev => ({ ...prev, [reviewId]: '' }));
    } finally {
      setReplying(prev => ({ ...prev, [reviewId]: false }));
    }
  };

  const handleAddComment = async (reviewId) => {
    if (!commentText[reviewId]?.trim()) return;
    
    setCommenting(prev => ({ ...prev, [reviewId]: true }));
    try {
      await onAddComment(reviewId, commentText[reviewId]);
      setCommentText(prev => ({ ...prev, [reviewId]: '' }));
    } finally {
      setCommenting(prev => ({ ...prev, [reviewId]: false }));
    }
  };

  const toggleReviewExpansion = async (reviewId) => {
    if (expandedReview === reviewId) {
      setExpandedReview(null);
    } else {
      setExpandedReview(reviewId);
      if (onLoadComments) {
        await onLoadComments(reviewId);
      }
    }
  };

  if (!reviews || reviews.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">إدارة التقييمات</h2>
        <div className="text-center py-8">
          <div className="text-gray-500 mb-4">
            <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
            </svg>
            <p className="text-lg">لا توجد تقييمات لهذا الكورس بعد</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">إدارة التقييمات</h2>
      
      <div className="space-y-6">
        {reviews.map((review) => (
          <div key={review.id} className="border border-gray-200 rounded-lg overflow-hidden">
            {/* رأس التقييم */}
            <div className="bg-gray-50 p-4">
              <div className="flex justify-between items-start">
                <div className="flex items-start space-x-3 rtl:space-x-reverse">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-semibold">
                      {review.user?.username?.charAt(0)?.toUpperCase() || "U"}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">
                      {review.user?.username || "مستخدم"}
                    </h4>
                    <div className="flex items-center mb-2">
                      {[...Array(5)].map((_, i) => (
                        <svg
                          key={i}
                          className={`w-4 h-4 ${
                            i < review.rating ? "text-yellow-400" : "text-gray-300"
                          }`}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                      <span className="ml-2 text-sm text-gray-500">
                        {new Date(review.created_at).toLocaleDateString('ar-EG')}
                      </span>
                    </div>
                    <p className="text-gray-700">{review.comment}</p>
                  </div>
                </div>

                {/* حالة التقييم وأزرار الإدارة */}
                <div className="flex flex-col items-end gap-2">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                    review.status === 'approved' 
                      ? 'bg-green-100 text-green-800' 
                      : review.status === 'rejected'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {review.status === 'approved' ? 'معتمد' : 
                     review.status === 'rejected' ? 'مرفوض' : 'في الانتظار'}
                  </span>

                  <div className="flex space-x-2 rtl:space-x-reverse">
                    {review.status !== 'approved' && (
                      <button
                        onClick={() => onApproveReview(review.id)}
                        disabled={approving}
                        className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 disabled:opacity-50"
                      >
                        {approving ? 'جاري الاعتماد...' : 'اعتماد'}
                      </button>
                    )}
                    
                    <button
                      onClick={() => onDeleteReview(review.id)}
                      className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700"
                    >
                      حذف
                    </button>
                    
                    <button
                      onClick={() => toggleReviewExpansion(review.id)}
                      className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
                    >
                      {expandedReview === review.id ? 'إخفاء' : 'تفاصيل'}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* التفاصيل المتوسعة */}
            {expandedReview === review.id && (
              <div className="p-4 border-t border-gray-200">
                {/* نموذج الرد */}
                <div className="mb-4">
                  <h5 className="font-semibold text-gray-800 mb-2">الرد على التقييم</h5>
                  <div className="flex gap-2">
                    <textarea
                      value={replyText[review.id] || ''}
                      onChange={(e) => setReplyText(prev => ({ ...prev, [review.id]: e.target.value }))}
                      placeholder="اكتب ردك على هذا التقييم..."
                      className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows={3}
                    />
                    <button
                      onClick={() => handleReply(review.id)}
                      disabled={replying[review.id] || !replyText[review.id]?.trim()}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
                    >
                      {replying[review.id] ? 'جاري الإرسال...' : 'إرسال الرد'}
                    </button>
                  </div>
                </div>

                {/* التعليقات */}
                <div>
                  <h5 className="font-semibold text-gray-800 mb-2">التعليقات</h5>
                  
                  {/* نموذج إضافة تعليق */}
                  <div className="mb-4">
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={commentText[review.id] || ''}
                        onChange={(e) => setCommentText(prev => ({ ...prev, [review.id]: e.target.value }))}
                        placeholder="إضافة تعليق..."
                        className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <button
                        onClick={() => handleAddComment(review.id)}
                        disabled={commenting[review.id] || !commentText[review.id]?.trim()}
                        className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50"
                      >
                        {commenting[review.id] ? 'جاري الإضافة...' : 'إضافة'}
                      </button>
                    </div>
                  </div>

                  {/* عرض التعليقات */}
                  {reviewComments[review.id] && reviewComments[review.id].length > 0 ? (
                    <div className="space-y-2">
                      {reviewComments[review.id].map((comment) => (
                        <div key={comment.id} className="bg-gray-50 p-3 rounded">
                          <div className="flex justify-between items-start">
                            <div>
                              <span className="font-medium text-sm text-gray-800">
                                {comment.user?.username || "مستخدم"}:
                              </span>
                              <span className="ml-2 text-gray-700">{comment.text}</span>
                            </div>
                            <span className="text-xs text-gray-500">
                              {new Date(comment.created_at).toLocaleDateString('ar-EG')}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">لا توجد تعليقات بعد</p>
                  )}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
