// Custom hook لإدارة بيانات الكورس للمعلم - زكي الخولي
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import {
  fetchInstructorCourse,
  fetchInstructorLessons,
  deleteInstructorLessons,
  fetchCourseVideo,
  approveReview,
  deleteReview,
  replyToReview,
  fetchReviewComments,
  addReviewComment,
} from "../services/instructor";

export function useInstructorCourse(courseId) {
  const router = useRouter();
  
  // حالات البيانات الأساسية - زكي الخولي
  const [courseData, setCourseData] = useState(null);
  const [lessons, setLessons] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // حالات الفيديو - زكي الخولي
  const [videoUrl, setVideoUrl] = useState(null);
  const [videoToken, setVideoToken] = useState(null);
  const [videoLoading, setVideoLoading] = useState(false);
  const [videoError, setVideoError] = useState(null);
  const [selectedLesson, setSelectedLesson] = useState(null);
  const [isClient, setIsClient] = useState(false);
  
  // حالات التقييمات - زكي الخولي
  const [approving, setApproving] = useState(false);
  const [reviewComments, setReviewComments] = useState({});
  const [commentLoading, setCommentLoading] = useState({});
  
  // حالات النشر - زكي الخولي
  const [publishing, setPublishing] = useState(false);
  
  // حالات إدارة الدروس - زكي الخولي
  const [expandedLesson, setExpandedLesson] = useState(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // جلب بيانات الكورس والدروس - زكي الخولي
  useEffect(() => {
    const loadData = async () => {
      try {
        const token = Cookies.get("authToken");
        if (!token) {
          setError("يرجى تسجيل الدخول للوصول إلى هذه الصفحة");
          router.push("/login");
          return;
        }

        const [courseResponse, lessonsResponse] = await Promise.all([
          fetchInstructorCourse(courseId, token),
          fetchInstructorLessons(courseId, token)
        ]);

        setCourseData(courseResponse);
        setLessons(lessonsResponse);
        setLoading(false);
      } catch (err) {
        console.error("خطأ في تحميل البيانات - زكي الخولي:", err);
        if (err.response?.status === 401) {
          Cookies.remove("authToken");
          setError("انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى");
          router.push("/login");
        } else {
          setError(err.response?.data?.message || "حدث خطأ أثناء تحميل البيانات");
        }
        setLoading(false);
      }
    };

    if (courseId) {
      loadData();
    }
  }, [courseId, router]);

  // دالة معالجة الفيديو - زكي الخولي
  const handleVideoClick = async (lesson) => {
    setVideoLoading(true);
    setVideoError(null);
    setSelectedLesson(lesson);

    try {
      const token = Cookies.get("authToken");
      const response = await fetchCourseVideo(lesson.id, token);
      
      if (response.video_url) {
        setVideoUrl(response.video_url);
        setVideoToken(response.token);
      } else {
        setVideoError("لم يتم العثور على رابط الفيديو");
      }
    } catch (err) {
      console.error("خطأ في جلب الفيديو - زكي الخولي:", err);
      setVideoError("حدث خطأ أثناء تحميل الفيديو");
    } finally {
      setVideoLoading(false);
    }
  };

  // دالة حذف الدرس - زكي الخولي
  const handleDeleteLesson = async (lessonId) => {
    try {
      const token = Cookies.get("authToken");
      await deleteInstructorLessons(lessonId, token);

      // تحديث قائمة الدروس
      setLessons(prev => prev.filter(lesson => lesson.id !== lessonId));

      // إخفاء الفيديو إذا كان الدرس المحذوف هو المعروض
      if (selectedLesson?.id === lessonId) {
        setVideoUrl(null);
        setSelectedLesson(null);
      }

      alert("تم حذف الدرس بنجاح");
    } catch (err) {
      console.error("خطأ في حذف الدرس - زكي الخولي:", err);
      alert("حدث خطأ أثناء حذف الدرس");
    }
  };

  // دالة توسيع/إخفاء الدرس - زكي الخولي
  const handleToggleLesson = (lessonId) => {
    setExpandedLesson(prev => prev === lessonId ? null : lessonId);
  };

  // دالة اعتماد التقييم - زكي الخولي
  const handleApproveReview = async (reviewId) => {
    setApproving(true);
    try {
      const token = Cookies.get("authToken");
      await approveReview(reviewId, token);
      
      // تحديث حالة التقييم في البيانات
      setCourseData(prev => ({
        ...prev,
        reviews: prev.reviews?.map(review => 
          review.id === reviewId 
            ? { ...review, status: 'approved' }
            : review
        ) || []
      }));
      
      alert("تم اعتماد التقييم بنجاح");
    } catch (err) {
      console.error("خطأ في اعتماد التقييم - زكي الخولي:", err);
      alert("حدث خطأ أثناء اعتماد التقييم");
    } finally {
      setApproving(false);
    }
  };

  // دالة حذف التقييم - زكي الخولي
  const handleDeleteReview = async (reviewId) => {
    if (!confirm("هل أنت متأكد من حذف هذا التقييم؟")) return;
    
    try {
      const token = Cookies.get("authToken");
      await deleteReview(reviewId, token);
      
      // إزالة التقييم من البيانات
      setCourseData(prev => ({
        ...prev,
        reviews: prev.reviews?.filter(review => review.id !== reviewId) || []
      }));
      
      alert("تم حذف التقييم بنجاح");
    } catch (err) {
      console.error("خطأ في حذف التقييم - زكي الخولي:", err);
      alert("حدث خطأ أثناء حذف التقييم");
    }
  };

  // دالة الرد على التقييم - زكي الخولي
  const handleReplyToReview = async (reviewId, replyText) => {
    try {
      const token = Cookies.get("authToken");
      await replyToReview(reviewId, replyText, token);
      alert("تم إرسال الرد بنجاح");
    } catch (err) {
      console.error("خطأ في الرد على التقييم - زكي الخولي:", err);
      alert("حدث خطأ أثناء إرسال الرد");
    }
  };

  // دالة تحميل تعليقات التقييم - زكي الخولي
  const handleLoadReviewComments = async (reviewId) => {
    setCommentLoading(prev => ({ ...prev, [reviewId]: true }));
    try {
      const token = Cookies.get("authToken");
      const comments = await fetchReviewComments(reviewId, token);
      setReviewComments(prev => ({ ...prev, [reviewId]: comments }));
    } catch (err) {
      console.error("خطأ في تحميل التعليقات - زكي الخولي:", err);
    } finally {
      setCommentLoading(prev => ({ ...prev, [reviewId]: false }));
    }
  };

  // دالة إضافة تعليق - زكي الخولي
  const handleAddComment = async (reviewId, commentText) => {
    try {
      const token = Cookies.get("authToken");
      const newComment = await addReviewComment(reviewId, commentText, token);
      
      // إضافة التعليق الجديد للقائمة
      setReviewComments(prev => ({
        ...prev,
        [reviewId]: [...(prev[reviewId] || []), newComment]
      }));
      
      alert("تم إضافة التعليق بنجاح");
    } catch (err) {
      console.error("خطأ في إضافة التعليق - زكي الخولي:", err);
      alert("حدث خطأ أثناء إضافة التعليق");
    }
  };

  // دالة نشر الكورس - زكي الخولي
  const handlePublishCourse = async () => {
    setPublishing(true);
    try {
      // سيتم تنفيذ هذه الدالة في مكون CourseManagement
      setCourseData(prev => ({ ...prev, is_published: true }));
    } finally {
      setPublishing(false);
    }
  };

  return {
    // البيانات الأساسية
    courseData,
    lessons,
    loading,
    error,
    
    // الفيديو
    videoUrl,
    videoToken,
    videoLoading,
    videoError,
    selectedLesson,
    isClient,
    handleVideoClick,
    
    // إدارة الدروس
    expandedLesson,
    handleToggleLesson,
    handleDeleteLesson,
    
    // التقييمات
    approving,
    reviewComments,
    commentLoading,
    handleApproveReview,
    handleDeleteReview,
    handleReplyToReview,
    handleLoadReviewComments,
    handleAddComment,
    
    // النشر
    publishing,
    handlePublishCourse,
  };
}
