// Custom hook لإدارة بيانات الكورس - زكي الخولي
import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { selectCurrentUser } from "../store/authSlice";
import Cookies from "js-cookie";
import {
  fetchStudentCourse,
  fetchCourseLessons,
  fetchCourseReviews,
  submitCourseReview,
  fetchReviewComments,
  toggleCourseLike,
  toggleCommentLike,
  addReviewReply,
  fetchExamStatus,
} from "../services/student";

export function useCourseData(courseId) {
  const router = useRouter();
  const user = useSelector(selectCurrentUser);
  
  // حالات البيانات الأساسية - زكي الخولي
  const [course, setCourse] = useState(null);
  const [lessons, setLessons] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isEnrolled, setIsEnrolled] = useState(false);
  
  // حالات الإعجاب - زكي الخولي
  const [isLiked, setIsLiked] = useState(false);
  const [likesCount, setLikesCount] = useState(0);
  const [likeLoading, setLikeLoading] = useState(false);
  const didInitLike = useRef(false);
  
  // حالات التقييمات - زكي الخولي
  const [reviews, setReviews] = useState([]);
  const [reviewLoading, setReviewLoading] = useState(false);
  const [reviewError, setReviewError] = useState(null);
  const [reviewSuccess, setReviewSuccess] = useState(null);
  
  // حالات الامتحانات - زكي الخولي
  const [examStatus, setExamStatus] = useState(null);
  const [quizStatuses, setQuizStatuses] = useState({});

  // إعادة تعيين الحالات عند تغيير معرف الكورس - زكي الخولي
  useEffect(() => {
    setCourse(null);
    setLessons([]);
    setLoading(true);
    setError(null);
    setIsEnrolled(false);
    setIsLiked(false);
    setLikesCount(0);
    didInitLike.current = false;
  }, [courseId]);

  // جلب بيانات الكورس الأساسية - زكي الخولي
  useEffect(() => {
    const controller = new AbortController();
    const loadCourseData = async () => {
      try {
        const token = Cookies.get("authToken");
        if (!token) {
          setError("يرجى تسجيل الدخول للوصول إلى هذه الصفحة");
          router.push("/login");
          return;
        }
        
        const courseData = await fetchStudentCourse(courseId, token, controller.signal);
        const lessonsData = await fetchCourseLessons(courseId, token, controller.signal);
        
        setCourse(courseData);
        setLessons(lessonsData);
        
        if (user && courseData.students) {
          const enrolled = courseData.students.some(s => s.id === user.id);
          setIsEnrolled(enrolled);
        } else {
          setIsEnrolled(false);
        }
        
        if (!didInitLike.current) {
          setIsLiked(!!courseData.is_liked);
          setLikesCount(courseData.likes_count || 0);
          didInitLike.current = true;
        }
        
        setLoading(false);
      } catch (err) {
        if (err.name === "AbortError") return;
        if (!controller.signal.aborted) {
          if (err.response?.status === 401) {
            Cookies.remove("authToken");
            setError("انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى");
            router.push("/login");
          } else {
            setError(err.response?.data?.message || "حدث خطأ أثناء تحميل الكورس");
          }
          setLoading(false);
        }
      }
    };
    
    loadCourseData();
    return () => controller.abort();
  }, [courseId, router, user]);

  // جلب التقييمات - زكي الخولي
  useEffect(() => {
    if (!courseId) return;
    setReviewLoading(true);
    fetchCourseReviews(courseId)
      .then(setReviews)
      .catch(() => setReviews([]))
      .finally(() => setReviewLoading(false));
  }, [courseId]);

  // جلب حالة الامتحان الرئيسي - زكي الخولي
  useEffect(() => {
    const examId = course?.exam_id;
    if (!examId) return;
    
    const fetchStatus = async () => {
      try {
        const token = Cookies.get("authToken");
        if (!token) return;
        const status = await fetchExamStatus(examId, token);
        setExamStatus(status);
      } catch (e) {
        console.error("تعذر جلب حالة الامتحان - زكي الخولي:", e);
      }
    };
    fetchStatus();
  }, [course?.exam_id]);

  // جلب حالة كل كويز لكل درس - زكي الخولي
  useEffect(() => {
    if (!lessons || !user) return;
    const token = Cookies.get("authToken");
    const fetchStatuses = async () => {
      const statuses = {};
      for (const lesson of lessons) {
        if (lesson.quizzes && lesson.quizzes.length > 0) {
          for (const quiz of lesson.quizzes) {
            try {
              const res = await fetchExamStatus(quiz.id, token);
              statuses[quiz.id] = res;
            } catch (e) {
              statuses[quiz.id] = null;
            }
          }
        }
      }
      setQuizStatuses(statuses);
    };
    fetchStatuses();
  }, [lessons, user]);

  // دالة تبديل الإعجاب - زكي الخولي
  const handleToggleLike = async () => {
    if (!user) {
      router.push("/login");
      return;
    }
    try {
      setLikeLoading(true);
      const token = Cookies.get("authToken");
      const res = await toggleCourseLike(courseId, token);
      setIsLiked(res.is_liked ?? res.liked ?? false);
      setLikesCount(res.likes_count ?? res.likesCount ?? 0);
    } catch (err) {
      console.error('toggle_like error:', err);
    } finally {
      setLikeLoading(false);
    }
  };

  // إرسال تعليق جديد - زكي الخولي
  const onSubmitReview = async (data) => {
    setReviewError(null);
    setReviewSuccess(null);
    try {
      const token = Cookies.get("authToken");
      await submitCourseReview(courseId, data, token);
      setReviewSuccess("تم إرسال التقييم بنجاح وسيظهر بعد المراجعة.");
      const res = await fetchCourseReviews(courseId);
      setReviews(res);
    } catch (err) {
      setReviewError("حدث خطأ أثناء إرسال التقييم");
    }
  };

  // جلب التعليقات الشجرية - زكي الخولي
  const fetchReviewCommentsHandler = async (reviewId) => {
    try {
      return await fetchReviewComments(reviewId);
    } catch {
      return [];
    }
  };

  // إعجاب/إلغاء إعجاب للتعليق أو الرد - زكي الخولي
  const handleToggleCommentLike = async (commentId) => {
    try {
      const token = Cookies.get("authToken");
      const res = await toggleCommentLike(commentId, token);
      setReviews((prev) => prev.map((review) => ({
        ...review,
        comments: review.comments?.map((c) =>
          updateCommentLikeRecursive(c, commentId, res)
        ) || [],
      })));
    } catch (err) {
      console.error('خطأ في تبديل إعجاب التعليق:', err);
    }
  };

  // إضافة رد - زكي الخولي
  const handleAddReply = async (parentId, replyText, reviewId) => {
    try {
      const token = Cookies.get("authToken");
      await addReviewReply(reviewId, parentId, replyText, token);
      const comments = await fetchReviewCommentsHandler(reviewId);
      setReviews((prev) => prev.map((review) =>
        review.id === reviewId ? { ...review, comments } : review
      ));
    } catch (err) {
      console.error('خطأ في إضافة الرد:', err);
    }
  };

  // تحديث حالة الامتحان بعد التسليم - زكي الخولي
  const handleExamSubmitted = async (quizId) => {
    try {
      const token = Cookies.get("authToken");
      if (!token) return;

      console.log('تحديث حالة الامتحان بعد التسليم - زكي الخولي:', quizId);
      const updatedStatus = await fetchExamStatus(quizId, token);
      setQuizStatuses(prev => ({
        ...prev,
        [quizId]: updatedStatus
      }));

      // إذا كان هذا هو الامتحان الرئيسي للكورس، حدث examStatus
      if (quizId === course?.exam_id) {
        setExamStatus(updatedStatus);
      }
      console.log('تم تحديث حالة الامتحان بنجاح - زكي الخولي');
    } catch (e) {
      console.error('خطأ في تحديث حالة الامتحان - زكي الخولي:', e);
    }
  };

  // دالة مساعدة لتحديث الإعجاب في التعليقات الشجرية - زكي الخولي
  const updateCommentLikeRecursive = (comment, commentId, likeData) => {
    if (comment.id === commentId) {
      return {
        ...comment,
        is_liked: likeData.is_liked ?? likeData.liked ?? false,
        likes_count: likeData.likes_count ?? likeData.likesCount ?? 0,
      };
    }
    if (comment.replies) {
      return {
        ...comment,
        replies: comment.replies.map(reply => 
          updateCommentLikeRecursive(reply, commentId, likeData)
        ),
      };
    }
    return comment;
  };

  return {
    // البيانات الأساسية
    course,
    lessons,
    loading,
    error,
    isEnrolled,
    
    // الإعجاب
    isLiked,
    likesCount,
    likeLoading,
    handleToggleLike,
    
    // التقييمات
    reviews,
    reviewLoading,
    reviewError,
    reviewSuccess,
    onSubmitReview,
    fetchReviewCommentsHandler,
    handleToggleCommentLike,
    handleAddReply,
    updateCommentLikeRecursive,
    
    // الامتحانات
    examStatus,
    quizStatuses,
    handleExamSubmitted,
  };
}
