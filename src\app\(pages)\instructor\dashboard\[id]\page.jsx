// صفحة إدارة الكورس للمعلم - تم إعادة تنظيمها باستخدام المكونات المنفصلة - زكي الخولي
"use client";
import React from "react";
import { useParams } from "next/navigation";
import "plyr/dist/plyr.css";

// استيراد المكونات المنفصلة - زكي الخولي
import CourseManagement from "../../../../../components/instructor/CourseManagement";
import LessonsManagement from "../../../../../components/instructor/LessonsManagement";
import ReviewsManagement from "../../../../../components/instructor/ReviewsManagement";

// استيراد المكونات المشتركة - زكي الخولي
import ErrorBoundary from "../../../../../components/common/ErrorBoundary";
import LoadingSpinner from "../../../../../components/common/LoadingSpinner";
import ErrorDisplay from "../../../../../components/common/ErrorDisplay";

// استيراد الـ hook المخصص - زكي الخولي
import { useInstructorCourse } from "../../../../../hooks/useInstructorCourse";

export default function CourseDetails() {
  const { id } = useParams();

  // استخدام الـ hook المخصص - زكي الخولي
  const {
    courseData,
    lessons,
    loading,
    error,
    videoUrl,
    videoLoading,
    videoError,
    selectedLesson,
    isClient,
    handleVideoClick,
    expandedLesson,
    handleToggleLesson,
    handleDeleteLesson,
    approving,
    reviewComments,
    commentLoading,
    handleApproveReview,
    handleDeleteReview,
    handleReplyToReview,
    handleLoadReviewComments,
    handleAddComment,
    publishing,
    handlePublishCourse,
  } = useInstructorCourse(id);

  // حالات التحميل والأخطاء مع المكونات المحسنة - زكي الخولي
  if (loading) {
    return <LoadingSpinner fullScreen message="جاري تحميل بيانات الكورس..." />;
  }

  if (error) {
    return <ErrorDisplay error={error} fullScreen />;
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-50 pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="space-y-8">
            {/* إدارة الكورس - زكي الخولي */}
            <CourseManagement
              courseData={courseData}
              onPublish={handlePublishCourse}
              publishing={publishing}
              onEditCourse={() => alert("سيتم إضافة صفحة تعديل الكورس قريباً")}
              onAddLesson={() => alert("سيتم إضافة صفحة إضافة درس جديد قريباً")}
              onManageExams={() => alert("سيتم إضافة صفحة إدارة الامتحانات قريباً")}
              onViewReports={() => alert("سيتم إضافة صفحة التقارير قريباً")}
            />

            {/* إدارة الدروس - زكي الخولي */}
            <LessonsManagement
              lessons={lessons}
              expandedLesson={expandedLesson}
              onToggleLesson={handleToggleLesson}
              onDeleteLesson={handleDeleteLesson}
              onVideoClick={handleVideoClick}
              videoUrl={videoUrl}
              videoLoading={videoLoading}
              videoError={videoError}
              isClient={isClient}
              selectedLesson={selectedLesson}
              onAddLesson={() => alert("سيتم إضافة صفحة إضافة درس جديد قريباً")}
              onEditLesson={(lessonId) => alert(`سيتم إضافة صفحة تعديل الدرس ${lessonId} قريباً`)}
              onEditQuiz={(quizId) => alert(`سيتم إضافة صفحة تعديل الامتحان ${quizId} قريباً`)}
              onDeleteQuiz={(quizId) => alert(`سيتم إضافة وظيفة حذف الامتحان ${quizId} قريباً`)}
              onAddQuiz={(lessonId) => alert(`سيتم إضافة صفحة إضافة امتحان للدرس ${lessonId} قريباً`)}
              onChangeFile={(lessonId) => alert(`سيتم إضافة وظيفة تغيير ملف الدرس ${lessonId} قريباً`)}
            />

            {/* إدارة التقييمات - زكي الخولي */}
            <ReviewsManagement
              reviews={courseData?.reviews || []}
              onApproveReview={handleApproveReview}
              onDeleteReview={handleDeleteReview}
              onReplyToReview={handleReplyToReview}
              approving={approving}
              reviewComments={reviewComments}
              onLoadComments={handleLoadReviewComments}
              onAddComment={handleAddComment}
            />
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
}