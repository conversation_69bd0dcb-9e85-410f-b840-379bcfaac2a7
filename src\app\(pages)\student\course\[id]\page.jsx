// صفحة الكورس للطالب - تم إعادة تنظيمها باستخدام المكونات المنفصلة - زكي الخولي
"use client";
import React from "react";
import { useParams } from "next/navigation";
import { useSelector } from "react-redux";
import { selectCurrentUser } from "../../../../../store/authSlice";
import { API_BASE_URL } from '../../../../../config/api';
import "plyr/dist/plyr.css";

// استيراد المكونات المنفصلة - زكي الخولي
import CourseHeader from "../../../../../components/course/CourseHeader";
import LessonsList from "../../../../../components/course/LessonsList";
import VideoPlayer from "../../../../../components/course/VideoPlayer";
import ExamModal from "../../../../../components/course/ExamModal";
import ReviewsSection from "../../../../../components/course/ReviewsSection";

// استيراد المكونات المشتركة - زكي الخولي
import ErrorBoundary from "../../../../../components/common/ErrorBoundary";
import LoadingSpinner from "../../../../../components/common/LoadingSpinner";
import ErrorDisplay from "../../../../../components/common/ErrorDisplay";

// استيراد الـ hooks المخصصة - زكي الخولي
import { useCourseData } from "../../../../../hooks/useCourseData";
import { useVideoPlayer } from "../../../../../hooks/useVideoPlayer";
import { useExamModal } from "../../../../../hooks/useExamModal";

export default function CoursePage() {
  const { id } = useParams();
  const user = useSelector(selectCurrentUser);

  // استخدام الـ hooks المخصصة - زكي الخولي
  const {
    course,
    lessons,
    loading,
    error,
    isEnrolled,
    isLiked,
    likesCount,
    likeLoading,
    handleToggleLike,
    reviews,
    reviewLoading,
    reviewError,
    reviewSuccess,
    onSubmitReview,
    fetchReviewCommentsHandler,
    handleToggleCommentLike,
    handleAddReply,
    updateCommentLikeRecursive,
    examStatus,
    quizStatuses,
    handleExamSubmitted,
  } = useCourseData(id);

  const {
    previewVideoUrl,
    previewLoading,
    previewError,
    selectedLesson,
    isClient,
    handleVideo,
    handleFileDownload,
    handleVideoError,
  } = useVideoPlayer();

  const {
    modalOpen,
    modalType,
    modalId,
    modalDuration,
    modalQuestions,
    modalExamStatus,
    handleOpenExamModal,
    handleCloseModal,
    handleExamSubmittedInModal,
  } = useExamModal();

  // دالة للحصول على رابط صورة الملف الشخصي - زكي الخولي
  const getProfileImageUrl = (path) => {
    if (!path) return null;
    if (path.startsWith("http://") || path.startsWith("https://")) {
      return path;
    }
    return `${API_BASE_URL}${path.startsWith("/") ? path : `/${path}`}`;
  };

  // معالجة النقر على زر الامتحان - زكي الخولي
  const handleExamClick = (examId) => {
    handleOpenExamModal(examId, handleExamSubmitted);
  };

  // معالجة النقر على الفيديو - زكي الخولي
  const handleVideoClick = (lesson) => {
    handleVideo(lesson, isEnrolled);
  };

  // معالجة تسليم الامتحان في المودال - زكي الخولي
  const onExamSubmittedInModal = (examId) => {
    handleExamSubmittedInModal(examId, handleExamSubmitted);
  };

  // حالات التحميل والأخطاء مع المكونات المحسنة - زكي الخولي
  if (loading) {
    return <LoadingSpinner fullScreen message="جاري تحميل بيانات الكورس..." />;
  }

  if (error) {
    return <ErrorDisplay error={error} fullScreen />;
  }

  if (!course) {
    return <ErrorDisplay error="لم يتم العثور على الكورس" fullScreen />;
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-50 pt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="space-y-8">
            {/* رأس الكورس - زكي الخولي */}
            <CourseHeader
              course={course}
              isEnrolled={isEnrolled}
              user={user}
              isLiked={isLiked}
              likesCount={likesCount}
              likeLoading={likeLoading}
              onToggleLike={handleToggleLike}
              getProfileImageUrl={getProfileImageUrl}
            />

            {/* مشغل الفيديو - زكي الخولي */}
            {isEnrolled && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h2 className="text-2xl font-bold text-gray-800 mb-6">مشغل الفيديو</h2>
                {previewLoading && (
                  <LoadingSpinner message="جاري تحميل الفيديو..." />
                )}
                {previewError && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                    <p className="text-red-600">{previewError}</p>
                  </div>
                )}
                <VideoPlayer
                  videoUrl={previewVideoUrl}
                  isClient={isClient}
                  onError={handleVideoError}
                  selectedLesson={selectedLesson}
                />
              </div>
            )}

            {/* قائمة الدروس - زكي الخولي */}
            <LessonsList
              lessons={lessons}
              isEnrolled={isEnrolled}
              selectedLesson={selectedLesson}
              onVideoClick={handleVideoClick}
              onExamClick={handleExamClick}
              onFileDownload={handleFileDownload}
              quizStatuses={quizStatuses}
              examId={course?.exam_id}
              assignmentId={course?.assignment_id}
            />

            {/* قسم التقييمات - زكي الخولي */}
            <ReviewsSection
              reviews={reviews}
              reviewLoading={reviewLoading}
              reviewError={reviewError}
              reviewSuccess={reviewSuccess}
              onSubmitReview={onSubmitReview}
              onToggleCommentLike={handleToggleCommentLike}
              onAddReply={handleAddReply}
              fetchReviewCommentsHandler={fetchReviewCommentsHandler}
              updateCommentLikeRecursive={updateCommentLikeRecursive}
            />
          </div>
        </div>

        {/* مودال الامتحان - زكي الخولي */}
        <ExamModal
          open={modalOpen}
          onClose={handleCloseModal}
          type={modalType}
          id={modalId}
          duration={modalDuration}
          questions={modalQuestions}
          examStatus={modalExamStatus}
          onExamSubmitted={onExamSubmittedInModal}
        />
      </div>
    </ErrorBoundary>
  );
}