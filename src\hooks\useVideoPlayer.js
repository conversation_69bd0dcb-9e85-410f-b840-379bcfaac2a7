// Custom hook لإدارة مشغل الفيديو - زكي الخولي
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";
import axios from "axios";
import { API_BASE_URL } from '../config/api';

export function useVideoPlayer() {
  const router = useRouter();
  const [previewVideoUrl, setPreviewVideoUrl] = useState(null);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [previewError, setPreviewError] = useState(null);
  const [selectedLesson, setSelectedLesson] = useState(null);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // دالة معالجة الفيديو - زكي الخولي
  const handleVideo = async (lesson, isEnrolled) => {
    setPreviewLoading(true);
    setPreviewError(null);
    setSelectedLesson(lesson);

    try {
      const token = Cookies.get("authToken");
      if (!token) {
        setPreviewError("يرجى تسجيل الدخول أولاً");
        return;
      }

      // التحقق من الصلاحيات قبل جلب الفيديو - زكي الخولي
      if (!lesson.is_preview && !isEnrolled) {
        setPreviewError("يجب عليك الاشتراك في الكورس لمشاهدة هذا الفيديو");
        return;
      }

      const headers = {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        Accept: "application/json",
      };

      console.log('جلب رابط الفيديو الآمن - زكي الخولي:', lesson.id);
      const response = await axios.get(
        `${API_BASE_URL}/api/lessons/${lesson.id}/video_url/`,
        {
          headers,
          timeout: 15000, // زيادة timeout للأمان
          validateStatus: function (status) {
            return status >= 200 && status < 500;
          },
        }
      );

      if (response.data.video_url) {
        console.log('تم جلب رابط HLS آمن - زكي الخولي');
        setPreviewVideoUrl(response.data.video_url);
      } else {
        setPreviewError("لم يتم العثور على رابط الفيديو");
      }
    } catch (err) {
      console.error("خطأ في جلب الفيديو - زكي الخولي:", err);

      if (err.code === "ECONNABORTED") {
        setPreviewError("انتهت مهلة الاتصال بالخادم");
      } else if (err.response) {
        console.error("Error response:", err.response.data);
        console.error("Error status:", err.response.status);

        if (err.response.status === 403) {
          setPreviewError("ليس لديك صلاحية لمشاهدة هذا الفيديو. يرجى الاشتراك في الكورس أولاً.");
        } else if (err.response.status === 401) {
          setPreviewError("انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.");
          Cookies.remove("authToken");
          router.push("/login");
        } else if (err.response.status === 404) {
          setPreviewError("الفيديو غير متوفر");
        } else {
          setPreviewError(`خطأ في الخادم: ${err.response.status}`);
        }
      } else if (err.request) {
        console.error("Error request:", err.request);
        setPreviewError("لا يمكن الاتصال بالخادم");
      } else {
        console.error("Error message:", err.message);
        setPreviewError("حدث خطأ أثناء تحميل الفيديو");
      }
    } finally {
      setPreviewLoading(false);
    }
  };

  // دالة تحميل الملف مع إخفاء رابط Cloudinary - زكي الخولي
  const handleFileDownload = async (fileUrl, fileName = 'lesson-file') => {
    try {
      // إظهار مؤشر التحميل
      const downloadButton = document.activeElement;
      const originalText = downloadButton.textContent;
      downloadButton.textContent = 'جاري التحميل...';
      downloadButton.disabled = true;

      // جلب الملف من Cloudinary
      const response = await fetch(fileUrl, {
        method: 'GET',
        headers: {
          'Accept': '*/*',
        },
      });

      if (!response.ok) {
        throw new Error('فشل في تحميل الملف');
      }

      // تحويل الاستجابة إلى blob
      const blob = await response.blob();
      
      // استخراج اسم الملف من URL أو استخدام اسم افتراضي
      const urlParts = fileUrl.split('/');
      const fileNameFromUrl = urlParts[urlParts.length - 1];
      const finalFileName = fileNameFromUrl.includes('.') ? fileNameFromUrl : `${fileName}.pdf`;

      // إنشاء رابط تحميل مؤقت
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = finalFileName;
      
      // إضافة الرابط للصفحة وتفعيله ثم حذفه
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // تنظيف الذاكرة
      window.URL.revokeObjectURL(downloadUrl);

      // إعادة تعيين النص الأصلي للزر
      downloadButton.textContent = originalText;
      downloadButton.disabled = false;

    } catch (error) {
      console.error('خطأ في تحميل الملف - زكي الخولي:', error);
      alert('حدث خطأ أثناء تحميل الملف. يرجى المحاولة مرة أخرى.');
      
      // إعادة تعيين النص الأصلي للزر في حالة الخطأ
      const downloadButton = document.activeElement;
      if (downloadButton) {
        downloadButton.textContent = 'تحميل ملف الدرس';
        downloadButton.disabled = false;
      }
    }
  };

  // دالة معالجة أخطاء الفيديو - زكي الخولي
  const handleVideoError = (errorMessage) => {
    setPreviewError(errorMessage);
  };

  // تنظيف البيانات - زكي الخولي
  const clearVideoData = () => {
    setPreviewVideoUrl(null);
    setPreviewError(null);
    setSelectedLesson(null);
  };

  return {
    previewVideoUrl,
    previewLoading,
    previewError,
    selectedLesson,
    isClient,
    handleVideo,
    handleFileDownload,
    handleVideoError,
    clearVideoData,
  };
}
