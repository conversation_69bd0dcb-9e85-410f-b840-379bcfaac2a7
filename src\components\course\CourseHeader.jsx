// رأس صفحة الكورس مع المعلومات الأساسية - تم فصله من الصفحة الرئيسية - زكي الخولي
"use client";
import React from "react";
import Image from "next/image";
import PaymentForm from "../../app/_Components/PaymentForm";

export default function CourseHeader({ 
  course, 
  isEnrolled, 
  user, 
  isLiked, 
  likesCount, 
  likeLoading, 
  onToggleLike,
  getProfileImageUrl 
}) {
  if (!course) return null;

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      {/* صورة الكورس */}
      <div className="relative h-96 w-full bg-gray-200 flex items-center justify-center">
        {course.thumbnail ? (
          <Image
            src={`https://res.cloudinary.com/di5y7hnub/${course.thumbnail}`}
            alt={course.title}
            fill
            className="object-cover"
            priority
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gray-100">
            <span className="text-gray-500 text-xl">
              لا توجد صورة للكورس
            </span>
          </div>
        )}
      </div>

      {/* معلومات الكورس */}
      <div className="p-6">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {course.title}
            </h1>
            <p className="text-gray-600 text-lg">
              {course.short_description}
            </p>
          </div>
          <div className="flex flex-col items-end gap-2">
            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <div className="flex items-center">
                <svg
                  className="w-5 h-5 text-yellow-400"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                <span className="ml-1 text-gray-600">
                  {course.rating || "لا يوجد تقييم"}
                </span>
              </div>
              <div className="flex items-center">
                <svg
                  className="w-5 h-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                  />
                </svg>
                <span className="ml-1 text-gray-600">
                  {course.students_count || 0} طالب
                </span>
              </div>
            </div>
            
            {/* زر الإعجاب */}
            <button
              onClick={onToggleLike}
              disabled={likeLoading}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                isLiked
                  ? "bg-red-100 text-red-600 hover:bg-red-200"
                  : "bg-gray-100 text-gray-600 hover:bg-gray-200"
              } ${likeLoading ? "opacity-50 cursor-not-allowed" : ""}`}
            >
              <svg
                className={`w-5 h-5 ${isLiked ? "fill-current" : ""}`}
                fill={isLiked ? "currentColor" : "none"}
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                />
              </svg>
              <span>{likesCount}</span>
            </button>
          </div>
        </div>

        {/* معلومات إضافية */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-700 mb-1">المستوى</h3>
            <p className="text-gray-600">{course.level}</p>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-700 mb-1">اللغة</h3>
            <p className="text-gray-600">{course.language}</p>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-700 mb-1">السعر</h3>
            <p className="text-gray-600">
              {course.discount_price ? (
                <>
                  <span className="line-through text-gray-400 mr-2">
                    {course.price} {course.currency}
                  </span>
                  <span className="text-green-600 font-bold">
                    {course.discount_price} {course.currency}
                  </span>
                </>
              ) : (
                `${course.price} ${course.currency}`
              )}
            </p>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-700 mb-1">المدرس</h3>
            <div className="flex items-center">
              {course.instructor?.profile_image && (
                <img
                  src={getProfileImageUrl(course.instructor.profile_image)}
                  alt={course.instructor.username}
                  className="w-8 h-8 rounded-full mr-2"
                />
              )}
              <p className="text-gray-600">{course.instructor?.username}</p>
            </div>
          </div>
        </div>

        {/* وصف الكورس */}
        <div className="mb-6">
          <h3 className="text-xl font-semibold text-gray-800 mb-3">وصف الكورس</h3>
          <p className="text-gray-600 leading-relaxed">{course.description}</p>
        </div>

        {/* زر الاشتراك أو رسالة الاشتراك */}
        {!isEnrolled ? (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-800 mb-3">
              اشترك في الكورس للوصول إلى جميع الدروس
            </h3>
            <PaymentForm
              courseId={course.id}
              courseTitle={course.title}
              price={course.discount_price || course.price}
              currency={course.currency}
              user={user}
            />
          </div>
        ) : (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <svg
                className="w-6 h-6 text-green-600 mr-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span className="text-green-800 font-semibold">
                أنت مشترك في هذا الكورس
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
