// Custom hook لمعالجة الأخطاء - زكي الخولي
import { useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import Cookies from "js-cookie";

export function useErrorHandler() {
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  // دالة معالجة الأخطاء العامة - زكي الخولي
  const handleError = useCallback((err, customMessage = null) => {
    console.error('خطأ تم التعامل معه - زكي الخولي:', err);
    
    let errorMessage = customMessage;
    
    if (!errorMessage) {
      if (err.response) {
        // خطأ من الخادم
        const status = err.response.status;
        const data = err.response.data;
        
        switch (status) {
          case 401:
            errorMessage = "انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى";
            Cookies.remove("authToken");
            router.push("/login");
            break;
          case 403:
            errorMessage = "ليس لديك صلاحية للوصول إلى هذا المحتوى";
            break;
          case 404:
            errorMessage = "المحتوى المطلوب غير موجود";
            break;
          case 422:
            errorMessage = data?.detail || data?.message || "البيانات المدخلة غير صحيحة";
            break;
          case 429:
            errorMessage = "تم تجاوز الحد المسموح من الطلبات، يرجى المحاولة لاحقاً";
            break;
          case 500:
            errorMessage = "خطأ في الخادم، يرجى المحاولة لاحقاً";
            break;
          default:
            errorMessage = data?.message || data?.detail || data?.error || "حدث خطأ غير متوقع";
        }
      } else if (err.request) {
        // خطأ في الشبكة
        errorMessage = "لا يمكن الاتصال بالخادم، يرجى التحقق من اتصال الإنترنت";
      } else {
        // خطأ آخر
        errorMessage = err.message || "حدث خطأ غير متوقع";
      }
    }
    
    setError(errorMessage);
    return errorMessage;
  }, [router]);

  // دالة تنفيذ عملية مع معالجة الأخطاء - زكي الخولي
  const executeWithErrorHandling = useCallback(async (asyncFunction, customErrorMessage = null) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await asyncFunction();
      return result;
    } catch (err) {
      const errorMessage = handleError(err, customErrorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [handleError]);

  // دالة مسح الخطأ - زكي الخولي
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // دالة إعادة المحاولة - زكي الخولي
  const retry = useCallback((asyncFunction, customErrorMessage = null) => {
    return executeWithErrorHandling(asyncFunction, customErrorMessage);
  }, [executeWithErrorHandling]);

  return {
    error,
    isLoading,
    handleError,
    executeWithErrorHandling,
    clearError,
    retry
  };
}

// Hook مخصص لمعالجة أخطاء النماذج - زكي الخولي
export function useFormErrorHandler() {
  const [fieldErrors, setFieldErrors] = useState({});
  const [generalError, setGeneralError] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFormError = useCallback((err) => {
    console.error('خطأ في النموذج - زكي الخولي:', err);
    
    if (err.response?.status === 422 && err.response.data?.errors) {
      // أخطاء التحقق من الحقول
      setFieldErrors(err.response.data.errors);
      setGeneralError(null);
    } else {
      // خطأ عام
      const message = err.response?.data?.message || err.response?.data?.detail || err.message || "حدث خطأ أثناء إرسال النموذج";
      setGeneralError(message);
      setFieldErrors({});
    }
  }, []);

  const submitForm = useCallback(async (submitFunction) => {
    setIsSubmitting(true);
    setFieldErrors({});
    setGeneralError(null);
    
    try {
      const result = await submitFunction();
      return result;
    } catch (err) {
      handleFormError(err);
      throw err;
    } finally {
      setIsSubmitting(false);
    }
  }, [handleFormError]);

  const clearErrors = useCallback(() => {
    setFieldErrors({});
    setGeneralError(null);
  }, []);

  const getFieldError = useCallback((fieldName) => {
    return fieldErrors[fieldName]?.[0] || null;
  }, [fieldErrors]);

  return {
    fieldErrors,
    generalError,
    isSubmitting,
    handleFormError,
    submitForm,
    clearErrors,
    getFieldError
  };
}

// Hook لمعالجة أخطاء التحميل - زكي الخولي
export function useAsyncOperation() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);

  const execute = useCallback(async (asyncFunction, onSuccess = null, onError = null) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await asyncFunction();
      setData(result);
      if (onSuccess) onSuccess(result);
      return result;
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || "حدث خطأ غير متوقع";
      setError(errorMessage);
      if (onError) onError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const reset = useCallback(() => {
    setLoading(false);
    setError(null);
    setData(null);
  }, []);

  return {
    loading,
    error,
    data,
    execute,
    reset
  };
}
