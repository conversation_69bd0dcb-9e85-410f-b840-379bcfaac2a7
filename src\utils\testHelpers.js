// مساعدات الاختبار والتحقق من الأداء - زكي الخولي
export const performanceMonitor = {
  // قياس وقت تحميل المكون
  measureComponentLoad: (componentName, startTime) => {
    const endTime = performance.now();
    const loadTime = endTime - startTime;
    console.log(`⏱️ ${componentName} تم تحميله في ${loadTime.toFixed(2)} مللي ثانية - زكي الخولي`);
    
    // تحذير إذا كان التحميل بطيئاً
    if (loadTime > 1000) {
      console.warn(`⚠️ ${componentName} يحتاج وقت طويل للتحميل - زكي الخولي`);
    }
    
    return loadTime;
  },

  // قياس استهلاك الذاكرة
  measureMemoryUsage: (componentName) => {
    if (performance.memory) {
      const memory = performance.memory;
      console.log(`🧠 ${componentName} - استهلاك الذاكرة:`, {
        used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
        total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
        limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`
      });
    }
  },

  // قياس عدد إعادة الرسم
  measureRerenders: (() => {
    const rerenderCounts = new Map();
    
    return (componentName) => {
      const count = rerenderCounts.get(componentName) || 0;
      rerenderCounts.set(componentName, count + 1);
      
      if (count > 10) {
        console.warn(`🔄 ${componentName} تم إعادة رسمه ${count} مرة - قد يحتاج تحسين - زكي الخولي`);
      }
      
      return count + 1;
    };
  })(),
};

// اختبار الاتصال بالباك إند
export const apiTester = {
  // اختبار الاتصال العام
  testConnection: async (baseUrl) => {
    try {
      const startTime = performance.now();
      const response = await fetch(`${baseUrl}/api/health/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      const endTime = performance.now();
      
      const responseTime = endTime - startTime;
      console.log(`🌐 اختبار الاتصال: ${response.status} في ${responseTime.toFixed(2)} مللي ثانية - زكي الخولي`);
      
      return {
        success: response.ok,
        status: response.status,
        responseTime,
      };
    } catch (error) {
      console.error('❌ فشل في الاتصال بالباك إند - زكي الخولي:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  },

  // اختبار endpoints محددة
  testEndpoints: async (baseUrl, token, endpoints) => {
    const results = {};
    
    for (const [name, endpoint] of Object.entries(endpoints)) {
      try {
        const startTime = performance.now();
        const response = await fetch(`${baseUrl}${endpoint}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
        const endTime = performance.now();
        
        results[name] = {
          success: response.ok,
          status: response.status,
          responseTime: endTime - startTime,
        };
        
        console.log(`✅ ${name}: ${response.status} في ${(endTime - startTime).toFixed(2)} مللي ثانية - زكي الخولي`);
      } catch (error) {
        results[name] = {
          success: false,
          error: error.message,
        };
        console.error(`❌ ${name}: ${error.message} - زكي الخولي`);
      }
    }
    
    return results;
  },
};

// اختبار المكونات
export const componentTester = {
  // اختبار تحميل المكون
  testComponentMount: (componentName, mountFunction) => {
    console.log(`🧪 اختبار تحميل ${componentName} - زكي الخولي`);
    const startTime = performance.now();
    
    try {
      const result = mountFunction();
      performanceMonitor.measureComponentLoad(componentName, startTime);
      console.log(`✅ ${componentName} تم تحميله بنجاح - زكي الخولي`);
      return { success: true, result };
    } catch (error) {
      console.error(`❌ فشل في تحميل ${componentName} - زكي الخولي:`, error);
      return { success: false, error: error.message };
    }
  },

  // اختبار props المطلوبة
  testRequiredProps: (componentName, props, requiredProps) => {
    console.log(`🔍 اختبار props مطلوبة لـ ${componentName} - زكي الخولي`);
    const missingProps = requiredProps.filter(prop => !(prop in props));
    
    if (missingProps.length > 0) {
      console.error(`❌ ${componentName} يفتقد props مطلوبة: ${missingProps.join(', ')} - زكي الخولي`);
      return { success: false, missingProps };
    }
    
    console.log(`✅ جميع props المطلوبة متوفرة لـ ${componentName} - زكي الخولي`);
    return { success: true };
  },

  // اختبار معالجة الأخطاء
  testErrorHandling: (componentName, errorFunction) => {
    console.log(`🛡️ اختبار معالجة الأخطاء لـ ${componentName} - زكي الخولي`);
    
    try {
      errorFunction();
      console.log(`✅ ${componentName} يتعامل مع الأخطاء بشكل صحيح - زكي الخولي`);
      return { success: true };
    } catch (error) {
      console.error(`❌ ${componentName} لا يتعامل مع الأخطاء بشكل صحيح - زكي الخولي:`, error);
      return { success: false, error: error.message };
    }
  },
};

// تحسين الأداء
export const performanceOptimizer = {
  // تحسين الصور
  optimizeImages: (imageUrls) => {
    console.log('🖼️ تحسين الصور - زكي الخولي');
    return imageUrls.map(url => {
      // إضافة معاملات تحسين Cloudinary
      if (url.includes('cloudinary.com')) {
        const optimizedUrl = url.replace('/upload/', '/upload/f_auto,q_auto,w_800/');
        console.log(`✅ تم تحسين الصورة: ${url} -> ${optimizedUrl} - زكي الخولي`);
        return optimizedUrl;
      }
      return url;
    });
  },

  // تحسين طلبات API
  optimizeApiCalls: (apiCalls) => {
    console.log('🚀 تحسين طلبات API - زكي الخولي');
    
    // دمج الطلبات المتشابهة
    const groupedCalls = apiCalls.reduce((groups, call) => {
      const key = `${call.method}-${call.endpoint.split('/')[1]}`;
      if (!groups[key]) groups[key] = [];
      groups[key].push(call);
      return groups;
    }, {});
    
    console.log(`✅ تم تجميع ${apiCalls.length} طلب إلى ${Object.keys(groupedCalls).length} مجموعة - زكي الخولي`);
    return groupedCalls;
  },

  // تحسين إعادة الرسم
  optimizeRerenders: (componentName, dependencies) => {
    console.log(`🔄 تحسين إعادة الرسم لـ ${componentName} - زكي الخولي`);
    
    // فحص التبعيات غير الضرورية
    const unnecessaryDeps = dependencies.filter(dep => 
      typeof dep === 'object' && dep !== null && !Array.isArray(dep)
    );
    
    if (unnecessaryDeps.length > 0) {
      console.warn(`⚠️ ${componentName} يحتوي على تبعيات قد تسبب إعادة رسم غير ضرورية - زكي الخولي`);
    }
    
    return {
      optimized: unnecessaryDeps.length === 0,
      suggestions: unnecessaryDeps.length > 0 ? 'استخدم useMemo أو useCallback للكائنات والدوال' : null
    };
  },
};

// تقرير شامل للأداء
export const generatePerformanceReport = (componentName, metrics) => {
  console.log(`📊 تقرير الأداء لـ ${componentName} - زكي الخولي`);
  console.log('='.repeat(50));
  
  Object.entries(metrics).forEach(([metric, value]) => {
    console.log(`${metric}: ${value}`);
  });
  
  console.log('='.repeat(50));
  
  // تقييم الأداء العام
  const loadTime = metrics.loadTime || 0;
  const rerenderCount = metrics.rerenderCount || 0;
  
  let performance = 'ممتاز';
  if (loadTime > 1000 || rerenderCount > 10) {
    performance = 'يحتاج تحسين';
  } else if (loadTime > 500 || rerenderCount > 5) {
    performance = 'جيد';
  }
  
  console.log(`🎯 التقييم العام: ${performance} - زكي الخولي`);
  return performance;
};
