// مكون إدارة الدروس للمعلم - تم فصله من الصفحة الرئيسية - زكي الخولي
"use client";
import React, { useState } from "react";
import VideoPlayer from "../course/VideoPlayer";

export default function LessonsManagement({ 
  lessons, 
  expandedLesson,
  onToggleLesson,
  onDeleteLesson,
  onVideoClick,
  videoUrl,
  videoLoading,
  videoError,
  isClient,
  selectedLesson
}) {
  const [deleteConfirm, setDeleteConfirm] = useState(null);

  const handleDeleteClick = (lessonId) => {
    setDeleteConfirm(lessonId);
  };

  const confirmDelete = () => {
    if (deleteConfirm && onDeleteLesson) {
      onDeleteLesson(deleteConfirm);
      setDeleteConfirm(null);
    }
  };

  if (!lessons || lessons.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">إدارة الدروس</h2>
        <div className="text-center py-8">
          <div className="text-gray-500 mb-4">
            <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
            <p className="text-lg">لا توجد دروس في هذا الكورس بعد</p>
          </div>
          <button className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
            إضافة أول درس
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* مشغل الفيديو */}
      {videoUrl && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-xl font-bold text-gray-800 mb-4">
            معاينة الفيديو: {selectedLesson?.title}
          </h3>
          {videoLoading && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">جاري تحميل الفيديو...</p>
            </div>
          )}
          {videoError && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <p className="text-red-600">{videoError}</p>
            </div>
          )}
          <VideoPlayer
            videoUrl={videoUrl}
            isClient={isClient}
            onError={() => {}}
            selectedLesson={selectedLesson}
          />
        </div>
      )}

      {/* قائمة الدروس */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800">إدارة الدروس</h2>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            إضافة درس جديد
          </button>
        </div>

        <div className="space-y-4">
          {lessons.map((lesson, index) => (
            <div
              key={lesson.id}
              className="border border-gray-200 rounded-lg overflow-hidden"
            >
              {/* رأس الدرس */}
              <div className="bg-gray-50 p-4 flex justify-between items-center">
                <div className="flex items-center space-x-4 rtl:space-x-reverse">
                  <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                    {lesson.order || index + 1}
                  </span>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800">
                      {lesson.title}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {lesson.lesson_type === "video" ? "درس فيديو" : "درس نصي"}
                      {lesson.is_preview && (
                        <span className="ml-2 bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                          معاينة مجانية
                        </span>
                      )}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  {/* زر معاينة الفيديو */}
                  {lesson.lesson_type === "video" && (
                    <button
                      onClick={() => onVideoClick(lesson)}
                      className="bg-green-600 text-white px-3 py-1 rounded hover:bg-green-700 transition-colors"
                    >
                      معاينة
                    </button>
                  )}
                  
                  {/* زر التوسيع */}
                  <button
                    onClick={() => onToggleLesson(lesson.id)}
                    className="bg-gray-600 text-white px-3 py-1 rounded hover:bg-gray-700 transition-colors"
                  >
                    {expandedLesson === lesson.id ? 'إخفاء' : 'عرض'}
                  </button>
                  
                  {/* زر التعديل */}
                  <button className="bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 transition-colors">
                    تعديل
                  </button>
                  
                  {/* زر الحذف */}
                  <button
                    onClick={() => handleDeleteClick(lesson.id)}
                    className="bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700 transition-colors"
                  >
                    حذف
                  </button>
                </div>
              </div>

              {/* تفاصيل الدرس المتوسعة */}
              {expandedLesson === lesson.id && (
                <div className="p-4 border-t border-gray-200">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* معلومات الدرس */}
                    <div>
                      <h4 className="font-semibold text-gray-800 mb-2">معلومات الدرس</h4>
                      <div className="space-y-2 text-sm">
                        <p><span className="font-medium">الوصف:</span> {lesson.description || 'لا يوجد وصف'}</p>
                        <p><span className="font-medium">المدة:</span> {lesson.duration || 'غير محدد'}</p>
                        <p><span className="font-medium">تاريخ الإنشاء:</span> {new Date(lesson.created_at).toLocaleDateString('ar-EG')}</p>
                      </div>
                    </div>

                    {/* الامتحانات والواجبات */}
                    <div>
                      <h4 className="font-semibold text-gray-800 mb-2">الامتحانات والواجبات</h4>
                      {lesson.quizzes && lesson.quizzes.length > 0 ? (
                        <div className="space-y-2">
                          {lesson.quizzes.map((quiz) => (
                            <div key={quiz.id} className="bg-gray-50 p-3 rounded">
                              <div className="flex justify-between items-center">
                                <div>
                                  <p className="font-medium text-sm">
                                    {quiz.quiz_type === 'exam' ? '📝 امتحان' : '📋 واجب'}: {quiz.title}
                                  </p>
                                  <p className="text-xs text-gray-500">{quiz.description}</p>
                                </div>
                                <div className="flex space-x-1 rtl:space-x-reverse">
                                  <button className="bg-blue-500 text-white px-2 py-1 rounded text-xs hover:bg-blue-600">
                                    تعديل
                                  </button>
                                  <button className="bg-red-500 text-white px-2 py-1 rounded text-xs hover:bg-red-600">
                                    حذف
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm text-gray-500">لا توجد امتحانات أو واجبات</p>
                      )}
                      <button className="mt-2 bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors">
                        إضافة امتحان/واجب
                      </button>
                    </div>
                  </div>

                  {/* ملفات الدرس */}
                  {lesson.resources && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <h4 className="font-semibold text-gray-800 mb-2">ملفات الدرس</h4>
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <a
                          href={lesson.resources}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="bg-blue-100 text-blue-800 px-3 py-1 rounded text-sm hover:bg-blue-200 transition-colors"
                        >
                          تحميل الملف
                        </a>
                        <button className="bg-gray-100 text-gray-800 px-3 py-1 rounded text-sm hover:bg-gray-200 transition-colors">
                          تغيير الملف
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* مودال تأكيد الحذف */}
      {deleteConfirm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-bold text-gray-900 mb-4">
              تأكيد حذف الدرس
            </h3>
            <p className="text-gray-600 mb-6">
              هل أنت متأكد من حذف هذا الدرس؟ لا يمكن التراجع عن هذا الإجراء.
            </p>
            <div className="flex gap-3 justify-end">
              <button
                onClick={() => setDeleteConfirm(null)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                إلغاء
              </button>
              <button
                onClick={confirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                حذف الدرس
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
