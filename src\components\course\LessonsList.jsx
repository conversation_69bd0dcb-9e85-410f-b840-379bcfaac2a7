// قائمة الدروس مع الامتحانات والواجبات - تم فصلها من الصفحة الرئيسية - زكي الخولي
"use client";
import React from "react";

export default function LessonsList({ 
  lessons, 
  isEnrolled, 
  selectedLesson,
  onVideoClick,
  onExamClick,
  onFileDownload,
  quizStatuses,
  examId,
  assignmentId
}) {
  if (!lessons || lessons.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">الدروس</h2>
        <div className="text-center py-8 text-gray-500">
          لا توجد دروس متاحة في هذا الكورس حالياً
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">الدروس</h2>
      <div className="space-y-4">
        {lessons.map((lesson, index) => (
          <div
            key={lesson.id}
            className={`border rounded-lg p-4 transition-all duration-200 ${
              selectedLesson?.id === lesson.id
                ? "border-blue-500 bg-blue-50"
                : "border-gray-200 hover:border-gray-300 hover:shadow-md"
            }`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                <div className="flex items-center justify-center w-10 h-10 bg-blue-100 text-blue-600 rounded-full font-semibold">
                  {lesson.order || index + 1}
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">
                    {lesson.title}
                  </h3>
                  <p className="text-sm text-gray-500">
                    {lesson.lesson_type === "video" ? "درس فيديو" : "درس نصي"}
                    {lesson.is_preview && (
                      <span className="ml-2 bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                        معاينة مجانية
                      </span>
                    )}
                  </p>
                  {lesson.description && (
                    <p className="text-sm text-gray-600 mt-1">
                      {lesson.description}
                    </p>
                  )}
                </div>
              </div>
              
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                {/* زر مشاهدة الفيديو */}
                {lesson.lesson_type === "video" && (
                  <button
                    onClick={() => onVideoClick(lesson)}
                    disabled={!lesson.is_preview && !isEnrolled}
                    className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                      !lesson.is_preview && !isEnrolled
                        ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                        : "bg-blue-600 text-white hover:bg-blue-700"
                    }`}
                  >
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z"
                      />
                    </svg>
                    مشاهدة
                  </button>
                )}
                
                {/* زر تحميل الملف */}
                {lesson.resources && isEnrolled && (
                  <button
                    onClick={() => onFileDownload(lesson.resources, lesson.title)}
                    className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                    تحميل ملف الدرس
                  </button>
                )}
              </div>
            </div>

            {/* الامتحانات والواجبات */}
            {lesson.quizzes && lesson.quizzes.length > 0 && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <h4 className="font-semibold text-gray-700 mb-3">الامتحانات والواجبات</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {lesson.quizzes.map((quiz) => {
                    const status = quizStatuses[quiz.id];
                    const isSubmitted = status?.status === 'submitted';
                    
                    return (
                      <div
                        key={quiz.id}
                        className="bg-gray-50 border rounded-lg p-3"
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="font-medium text-gray-800">
                              {quiz.quiz_type === 'exam' ? '📝 امتحان' : '📋 واجب'}: {quiz.title}
                            </h5>
                            <p className="text-sm text-gray-600">{quiz.description}</p>
                            <div className="text-xs text-gray-500 mt-1">
                              {quiz.quiz_type === 'exam' && (
                                <>
                                  درجة النجاح: {quiz.passing_score} | 
                                  الوقت: {quiz.time_limit > 0 ? `${quiz.time_limit} دقيقة` : 'غير محدود'}
                                </>
                              )}
                              {quiz.quiz_type === 'assignment' && (
                                <>وقت غير محدود</>
                              )}
                            </div>
                          </div>
                          
                          {isEnrolled && (
                            <button
                              onClick={() => onExamClick(quiz.id)}
                              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                                isSubmitted
                                  ? "bg-green-100 text-green-700 hover:bg-green-200"
                                  : quiz.quiz_type === 'exam'
                                  ? "bg-red-100 text-red-700 hover:bg-red-200"
                                  : "bg-blue-100 text-blue-700 hover:bg-blue-200"
                              }`}
                            >
                              {isSubmitted ? "إظهار النتيجة" : 
                               quiz.quiz_type === 'exam' ? "حل الامتحان" : "حل الواجب"}
                            </button>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* امتحان الكورس الرئيسي */}
      {examId && isEnrolled && (
        <div className="mt-6 bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-red-800 mb-2">امتحان الكورس</h3>
          <p className="text-red-600 mb-4">
            امتحان شامل لجميع محتويات الكورس
          </p>
          <button
            onClick={() => onExamClick(examId)}
            className="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors"
          >
            {quizStatuses[examId]?.status === 'submitted' ? "إظهار نتيجة الامتحان" : "بدء امتحان الكورس"}
          </button>
        </div>
      )}

      {/* واجب الكورس الرئيسي */}
      {assignmentId && isEnrolled && (
        <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-blue-800 mb-2">واجب الكورس</h3>
          <p className="text-blue-600 mb-4">
            واجب شامل لجميع محتويات الكورس
          </p>
          <button
            onClick={() => onExamClick(assignmentId)}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            {quizStatuses[assignmentId]?.status === 'submitted' ? "إظهار نتيجة الواجب" : "بدء واجب الكورس"}
          </button>
        </div>
      )}
    </div>
  );
}
